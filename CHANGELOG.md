# Change Log

All notable changes to the "FastCode" extension will be documented in this file.

## [0.1.0] - 2024-01-XX

### Added
- Initial release of FastCode - Offline AI Assistant
- **Inline Chat (Ctrl+K)**: Edit code directly with AI assistance
- **Composer (Ctrl+I)**: Generate multiple files from natural language
- **Global Chat (Ctrl+L)**: Ask questions about your codebase
- **Auto Debug (Alt+Enter)**: Automatically fix errors with AI
- **Test Generation**: Generate unit tests for selected code
- **Documentation Generation**: Create docs in multiple formats
- **Terminal Commands**: Convert natural language to shell commands
- **Semantic Search**: Find code using natural language
- **Real-time Code Indexing**: Powered by Tree-sitter
- **Multi-language Support**: TypeScript, JavaScript, Python, Java, C++, Rust, Go
- **Local AI Integration**: Support for Ollama, LM Studio, and OpenAI-compatible APIs

### Features
- Tree-sitter based syntax parsing for accurate code understanding
- SQLite-based semantic indexing for fast code search
- Real-time file watching and incremental index updates
- Diff preview for code changes with Apply/Reject/Retry options
- Multiple test framework support (Jest, <PERSON><PERSON>, pytest, JUnit, etc.)
- Multiple documentation formats (JSDoc, Google Style, NumPy, etc.)
- Cross-platform terminal command generation
- Webview-based UI for chat and composer interfaces
- Configurable AI model settings and endpoints

### Supported Languages
- TypeScript (.ts, .tsx)
- JavaScript (.js, .jsx)
- Python (.py)
- Java (.java)
- C++ (.cpp, .cc, .cxx, .h, .hpp)
- C (.c, .h)
- Rust (.rs)
- Go (.go)

### Supported AI Models
- CodeLlama (7B, 13B, 34B)
- WizardCoder (7B, 13B)
- DeepSeek Coder (6.7B, 33B)
- Phind CodeLlama (34B)
- Any OpenAI-compatible model

### Configuration Options
- `fastcode.apiEndpoint`: Local AI model API endpoint
- `fastcode.modelName`: Name of the AI model to use
- `fastcode.maxTokens`: Maximum tokens for AI responses
- `fastcode.temperature`: Temperature for AI model
- `fastcode.enableContextIndex`: Enable/disable code indexing
- `fastcode.indexedFileTypes`: File types to include in indexing

### Known Issues
- Large codebases may take time to index initially
- Some complex code patterns may not be parsed perfectly
- Memory usage scales with codebase size

### Requirements
- VSCode 1.74.0 or higher
- Local AI model (Ollama, LM Studio, etc.)
- Node.js dependencies for Tree-sitter parsers

---

## Future Releases

### Planned Features
- **Code Refactoring**: AI-powered code refactoring suggestions
- **Code Review**: Automated code review with suggestions
- **Git Integration**: AI-powered commit message generation
- **Plugin System**: Support for custom AI providers
- **Performance Improvements**: Faster indexing and search
- **More Languages**: Support for additional programming languages
- **Cloud Sync**: Optional cloud synchronization for settings
- **Team Features**: Shared AI models and configurations

### Roadmap
- v0.2.0: Code refactoring and review features
- v0.3.0: Git integration and commit assistance
- v0.4.0: Plugin system and custom providers
- v0.5.0: Performance optimizations and cloud features
- v1.0.0: Stable release with full feature set
