# FastCode - 项目概览

## 项目简介

FastCode 是一个为 VSCode 设计的离线 AI 编程助手，提供类似 Cursor 的功能，但完全在本地运行。它支持多种本地大语言模型（如 Ollama、LM Studio），无需联网即可提供强大的 AI 编程辅助功能。

## 核心特性

### 🔥 主要功能
- **行内对话 (Ctrl+K)**: 直接在编辑器中修改代码，支持 diff 预览
- **多文件生成器 (Ctrl+I)**: 从自然语言描述生成完整的项目结构
- **全局对话 (Ctrl+L)**: 基于代码库上下文的智能问答
- **自动调试 (Alt+Enter)**: AI 驱动的错误自动修复

### 🛠️ 开发工具
- **测试生成**: 为任意代码生成全面的单元测试
- **文档生成**: 支持多种格式的文档自动生成
- **终端命令**: 自然语言转换为 shell 命令
- **语义搜索**: 使用自然语言搜索代码

### 🧠 智能上下文系统
- **实时代码索引**: 基于 Tree-sitter 的语法解析
- **语义向量搜索**: 在整个工作区中查找相关代码
- **多语言支持**: TypeScript、JavaScript、Python、Java、C++、Rust、Go 等
- **增量更新**: 高效的文件监听和索引更新

## 项目结构

```
fastcode-vscode/
├── src/                          # 源代码
│   ├── extension.ts              # 扩展入口点
│   ├── services/                 # 核心服务
│   │   ├── aiModelService.ts     # AI 模型通信服务
│   │   └── contextIndexService.ts # 代码索引服务
│   ├── providers/                # 功能提供者
│   │   ├── inlineChatProvider.ts    # 行内对话
│   │   ├── composerProvider.ts      # 多文件生成器
│   │   ├── globalChatProvider.ts    # 全局对话
│   │   ├── autoDebugProvider.ts     # 自动调试
│   │   ├── testGeneratorProvider.ts # 测试生成
│   │   ├── docGeneratorProvider.ts  # 文档生成
│   │   ├── terminalCommandProvider.ts # 终端命令
│   │   └── semanticSearchProvider.ts  # 语义搜索
│   └── test/                     # 测试文件
├── docs/                         # 文档
│   ├── INSTALLATION.md           # 安装指南
│   └── USER_GUIDE.md            # 用户指南
├── scripts/                      # 安装脚本
│   ├── setup.sh                 # Linux/Mac 安装脚本
│   └── setup.bat                # Windows 安装脚本
├── package.json                  # 扩展配置
├── tsconfig.json                # TypeScript 配置
├── README.md                    # 项目说明
└── CHANGELOG.md                 # 更新日志
```

## 技术架构

### 核心技术栈
- **TypeScript**: 主要开发语言
- **VSCode Extension API**: 扩展开发框架
- **Tree-sitter**: 语法解析和代码理解
- **SQLite**: 本地代码索引存储
- **Axios**: HTTP 客户端用于 AI 模型通信

### AI 模型集成
- **Ollama**: 主要推荐的本地 AI 服务
- **LM Studio**: 替代的本地 AI 服务
- **OpenAI 兼容 API**: 支持任何兼容的 API 端点

### 支持的编程语言
- TypeScript/JavaScript (.ts, .tsx, .js, .jsx)
- Python (.py)
- Java (.java)
- C/C++ (.c, .cpp, .h, .hpp)
- Rust (.rs)
- Go (.go)

## 快速开始

### 系统要求
- VSCode 1.74.0+
- Node.js 16+
- 8GB+ RAM (推荐 16GB+)
- 本地 AI 模型服务

### 安装步骤

1. **克隆项目**:
```bash
git clone <repository-url>
cd fastcode-vscode
```

2. **运行安装脚本**:
```bash
# Linux/Mac
chmod +x scripts/setup.sh
./scripts/setup.sh

# Windows
scripts\setup.bat
```

3. **手动安装** (可选):
```bash
npm install
npm run compile
npm run package
code --install-extension fastcode-*.vsix
```

### 配置 AI 模型

**Ollama (推荐)**:
```bash
# 安装 Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 启动服务
ollama serve

# 下载模型
ollama pull codellama:7b
```

**配置 FastCode**:
```json
{
  "fastcode.apiEndpoint": "http://localhost:11434",
  "fastcode.modelName": "codellama:7b",
  "fastcode.maxTokens": 2048,
  "fastcode.temperature": 0.1
}
```

## 使用方法

### 基本操作
- **Ctrl+K**: 选择代码后使用行内对话修改
- **Ctrl+I**: 打开多文件生成器
- **Ctrl+L**: 打开全局对话面板
- **Alt+Enter**: 在错误行自动修复

### 高级功能
- **右键菜单**: 生成测试和文档
- **语义搜索**: 使用自然语言搜索代码
- **终端命令**: 自然语言转换为命令行

## 开发指南

### 开发环境设置
```bash
npm install
npm run watch  # 开发模式
```

### 测试
```bash
npm run test
npm run lint
```

### 打包发布
```bash
npm run package
```

## 贡献指南

欢迎贡献代码！请查看以下领域：
- 新语言支持
- AI 模型集成
- UI/UX 改进
- 性能优化
- 错误修复

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 致谢

- 灵感来源于 [Cursor](https://cursor.sh/)
- 使用 [Tree-sitter](https://tree-sitter.github.io/) 进行语法解析
- 支持 [Ollama](https://ollama.ai/) 和 [LM Studio](https://lmstudio.ai/) 本地模型

## 支持

- 🐛 **问题报告**: GitHub Issues
- 💡 **功能请求**: GitHub Discussions
- 📖 **文档**: 项目 Wiki

---

**FastCode** - 将 AI 的力量带到您的本地开发环境！🚀
