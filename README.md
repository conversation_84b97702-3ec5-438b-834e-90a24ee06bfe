# FastCode - Offline AI Assistant for VSCode

FastCode is a powerful offline AI-powered coding assistant for Visual Studio Code that brings Cursor-like functionality to your local development environment. It works entirely offline with local Large Language Models (LLMs) like Ollama, LM Studio, and other OpenAI-compatible APIs.

## ✨ Features

### 🔥 Core AI Features
- **Inline Chat (Ctrl+K)**: Edit code directly with AI assistance using floating diff previews
- **Composer (Ctrl+I)**: Generate multiple files and project scaffolds from natural language descriptions
- **Global Chat (Ctrl+L)**: Ask questions about your codebase with semantic context awareness
- **Auto Debug (Alt+Enter)**: Automatically fix errors with AI-generated patches

### 🛠️ Development Tools
- **Test Generation**: Generate comprehensive unit tests for any code selection
- **Documentation Generation**: Create detailed documentation in multiple formats (JSDoc, Google Style, etc.)
- **Terminal Commands**: Convert natural language to shell commands
- **Semantic Search**: Find code using natural language instead of exact text matching

### 🧠 Smart Context System
- **Real-time Code Indexing**: Powered by Tree-sitter for accurate syntax understanding
- **Semantic Vector Search**: Find relevant code across your entire workspace
- **Multi-language Support**: TypeScript, JavaScript, Python, Java, C++, Rust, Go, and more
- **Incremental Updates**: Efficient file watching and index updates

## 🚀 Quick Start

### Prerequisites
1. **VSCode** 1.74.0 or higher
2. **Local AI Model** (choose one):
   - [Ollama](https://ollama.ai/) (Recommended)
   - [LM Studio](https://lmstudio.ai/)
   - Any OpenAI-compatible API endpoint

### Installation

1. **Install the Extension**:
   ```bash
   # Clone the repository
   git clone <repository-url>
   cd fastcode-vscode
   
   # Install dependencies
   npm install
   
   # Compile the extension
   npm run compile
   
   # Package the extension
   npm run package
   ```

2. **Install in VSCode**:
   - Open VSCode
   - Go to Extensions (Ctrl+Shift+X)
   - Click "..." → "Install from VSIX..."
   - Select the generated `.vsix` file

3. **Setup Local AI Model**:

   **Option A: Ollama (Recommended)**
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Pull a code model
   ollama pull codellama:7b
   # or for better performance (requires more RAM):
   ollama pull codellama:13b
   ```

   **Option B: LM Studio**
   - Download and install [LM Studio](https://lmstudio.ai/)
   - Download a code model (e.g., CodeLlama, WizardCoder)
   - Start the local server

4. **Configure FastCode**:
   - Open VSCode Settings (Ctrl+,)
   - Search for "FastCode"
   - Set your API endpoint (default: `http://localhost:11434` for Ollama)
   - Set your model name (e.g., `codellama:7b`)

## 🎯 Usage

### Inline Chat (Ctrl+K)
1. Select code in your editor
2. Press `Ctrl+K` (or `Cmd+K` on Mac)
3. Type your request (e.g., "make this async", "add error handling")
4. Review the diff and apply/reject changes

### Composer (Ctrl+I)
1. Press `Ctrl+I` to open the Composer panel
2. Describe what you want to build (e.g., "Create a REST API with Express and TypeScript")
3. Review generated files
4. Apply all files to your workspace

### Global Chat (Ctrl+L)
1. Press `Ctrl+L` to open the chat panel
2. Ask questions about your code
3. Click on file references to jump to relevant code

### Auto Debug (Alt+Enter)
1. Place cursor on a line with an error
2. Press `Alt+Enter`
3. Choose from AI-generated fix suggestions
4. Apply the fix

### Generate Tests
1. Select a function or class
2. Right-click → "Generate Unit Tests"
3. Choose your testing framework
4. Review and save the generated tests

### Generate Documentation
1. Select code to document
2. Right-click → "Generate Documentation"
3. Choose documentation format
4. Select where to place the docs

## ⚙️ Configuration

### Basic Settings
```json
{
  "fastcode.apiEndpoint": "http://localhost:11434",
  "fastcode.modelName": "codellama:7b",
  "fastcode.maxTokens": 2048,
  "fastcode.temperature": 0.1,
  "fastcode.enableContextIndex": true
}
```

### Supported Models
- **CodeLlama**: `codellama:7b`, `codellama:13b`, `codellama:34b`
- **WizardCoder**: `wizardcoder:7b`, `wizardcoder:13b`
- **DeepSeek Coder**: `deepseek-coder:6.7b`, `deepseek-coder:33b`
- **Phind CodeLlama**: `phind-codellama:34b`

### Language Support
- TypeScript/JavaScript
- Python
- Java
- C/C++
- Rust
- Go
- And more...

## 🔧 Advanced Configuration

### Custom API Endpoints
FastCode supports multiple API formats:
- Ollama API (`/api/generate`)
- OpenAI-compatible APIs (`/v1/completions`)
- Custom endpoints

### Performance Tuning
- **Model Size**: Larger models (13B, 34B) provide better results but require more RAM
- **Context Window**: Adjust `maxTokens` based on your model's capabilities
- **Temperature**: Lower values (0.1-0.3) for code generation, higher for creative tasks

## 🛠️ Development

### Building from Source
```bash
# Clone and setup
git clone <repository-url>
cd fastcode-vscode
npm install

# Development
npm run watch    # Watch mode for development
npm run compile  # One-time compilation
npm run lint     # Run linter
npm run test     # Run tests

# Packaging
npm run package  # Create .vsix file
```

### Project Structure
```
src/
├── extension.ts              # Main extension entry point
├── services/
│   ├── aiModelService.ts     # AI model communication
│   └── contextIndexService.ts # Code indexing and search
└── providers/
    ├── inlineChatProvider.ts    # Inline chat functionality
    ├── composerProvider.ts      # Multi-file generation
    ├── globalChatProvider.ts    # Global chat panel
    ├── autoDebugProvider.ts     # Error fixing
    ├── testGeneratorProvider.ts # Test generation
    ├── docGeneratorProvider.ts  # Documentation generation
    ├── terminalCommandProvider.ts # Command generation
    └── semanticSearchProvider.ts  # Semantic search
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Areas for Contribution
- Additional language support
- New AI model integrations
- UI/UX improvements
- Performance optimizations
- Bug fixes and testing

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [Cursor](https://cursor.sh/)
- Built with [Tree-sitter](https://tree-sitter.github.io/) for syntax parsing
- Powered by local LLMs via [Ollama](https://ollama.ai/) and [LM Studio](https://lmstudio.ai/)

## 📞 Support

- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/your-repo/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/your-repo/discussions)
- 📖 **Documentation**: [Wiki](https://github.com/your-repo/wiki)

---

**FastCode** - Bringing the power of AI to your local development environment! 🚀
