# FastCode Installation Guide

This guide will help you install and configure FastCode for offline AI-powered coding assistance.

## Prerequisites

### System Requirements
- **VSCode**: Version 1.74.0 or higher
- **Node.js**: Version 16.x or higher (for development)
- **RAM**: Minimum 8GB (16GB+ recommended for larger models)
- **Storage**: 2-10GB for AI models (depending on model size)

### Operating System Support
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Linux (Ubuntu 18.04+, other distributions)

## Step 1: Install Local AI Model

Choose one of the following options for running AI models locally:

### Option A: Ollama (Recommended)

**Windows:**
```powershell
# Download and run the installer from https://ollama.ai/download
# Or use winget:
winget install Ollama.Ollama
```

**macOS:**
```bash
# Download from https://ollama.ai/download
# Or use Homebrew:
brew install ollama
```

**Linux:**
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve
```

**Pull a Code Model:**
```bash
# Recommended models (choose based on your RAM):
ollama pull codellama:7b      # ~4GB RAM, good performance
ollama pull codellama:13b     # ~8GB RAM, better performance
ollama pull codellama:34b     # ~20GB RAM, best performance

# Alternative models:
ollama pull wizardcoder:7b    # Good for general coding
ollama pull deepseek-coder:6.7b  # Excellent for code completion
```

### Option B: LM Studio

1. Download [LM Studio](https://lmstudio.ai/) for your platform
2. Install and launch LM Studio
3. Browse and download a code model:
   - CodeLlama 7B/13B/34B
   - WizardCoder 7B/13B
   - DeepSeek Coder 6.7B/33B
4. Start the local server in LM Studio
5. Note the server URL (usually `http://localhost:1234`)

### Option C: Other OpenAI-Compatible APIs

FastCode works with any OpenAI-compatible API endpoint:
- **LocalAI**
- **Text Generation WebUI**
- **vLLM**
- **Custom implementations**

## Step 2: Install FastCode Extension

### Method 1: From Source (Current)

1. **Clone the Repository:**
```bash
git clone <repository-url>
cd fastcode-vscode
```

2. **Install Dependencies:**
```bash
npm install
```

3. **Compile the Extension:**
```bash
npm run compile
```

4. **Package the Extension:**
```bash
npm run package
```

5. **Install in VSCode:**
   - Open VSCode
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Extensions: Install from VSIX..."
   - Select the generated `fastcode-*.vsix` file

### Method 2: From Marketplace (Future)

Once published to the VSCode Marketplace:
1. Open VSCode
2. Go to Extensions (`Ctrl+Shift+X`)
3. Search for "FastCode"
4. Click "Install"

## Step 3: Configure FastCode

### Basic Configuration

1. Open VSCode Settings (`Ctrl+,` or `Cmd+,`)
2. Search for "FastCode"
3. Configure the following settings:

```json
{
  "fastcode.apiEndpoint": "http://localhost:11434",
  "fastcode.modelName": "codellama:7b",
  "fastcode.maxTokens": 2048,
  "fastcode.temperature": 0.1,
  "fastcode.enableContextIndex": true
}
```

### Configuration for Different Setups

**Ollama (Default):**
```json
{
  "fastcode.apiEndpoint": "http://localhost:11434",
  "fastcode.modelName": "codellama:7b"
}
```

**LM Studio:**
```json
{
  "fastcode.apiEndpoint": "http://localhost:1234",
  "fastcode.modelName": "your-model-name"
}
```

**Custom API:**
```json
{
  "fastcode.apiEndpoint": "http://your-server:port",
  "fastcode.modelName": "your-model-name"
}
```

### Advanced Configuration

```json
{
  "fastcode.apiEndpoint": "http://localhost:11434",
  "fastcode.modelName": "codellama:13b",
  "fastcode.maxTokens": 4096,
  "fastcode.temperature": 0.1,
  "fastcode.enableContextIndex": true,
  "fastcode.indexedFileTypes": [
    "typescript",
    "javascript",
    "python",
    "java",
    "cpp",
    "c",
    "rust",
    "go"
  ]
}
```

## Step 4: Verify Installation

### Test AI Connection

1. Open any code file in VSCode
2. Select some code
3. Press `Ctrl+K` (or `Cmd+K` on Mac)
4. Type a simple request like "add comments"
5. If working correctly, you should see a diff preview

### Test Context Indexing

1. Open a project with multiple files
2. Wait for indexing to complete (check status bar)
3. Press `Ctrl+L` to open Global Chat
4. Ask a question about your code
5. You should get contextually relevant responses

## Troubleshooting

### Common Issues

**1. "Cannot connect to AI model" Error**
- Ensure your AI service is running (Ollama/LM Studio)
- Check the API endpoint in settings
- Verify the model is downloaded and available

**2. "No response from AI" Error**
- Check if the model name matches exactly
- Increase timeout settings if using a slow model
- Try a smaller model if running out of memory

**3. Context Indexing Not Working**
- Ensure `fastcode.enableContextIndex` is `true`
- Check if your file types are in `indexedFileTypes`
- Restart VSCode to reinitialize indexing

**4. Extension Not Loading**
- Check VSCode version (must be 1.74.0+)
- Look for errors in Developer Console (`Help > Toggle Developer Tools`)
- Try reinstalling the extension

### Performance Optimization

**For Better Performance:**
- Use SSD storage for faster file indexing
- Increase RAM for larger models
- Close unnecessary applications
- Use smaller models on resource-constrained systems

**Model Recommendations by System:**
- **8GB RAM**: codellama:7b, wizardcoder:7b
- **16GB RAM**: codellama:13b, deepseek-coder:6.7b
- **32GB+ RAM**: codellama:34b, deepseek-coder:33b

### Getting Help

If you encounter issues:

1. **Check the logs**: Open Developer Console in VSCode
2. **Verify configuration**: Double-check all settings
3. **Test AI service**: Try accessing the API directly
4. **Report issues**: Create an issue on GitHub with:
   - VSCode version
   - FastCode version
   - AI model and service used
   - Error messages and logs
   - Steps to reproduce

## Next Steps

Once installed, check out:
- [User Guide](USER_GUIDE.md) - Learn how to use all features
- [Configuration Guide](CONFIGURATION.md) - Advanced configuration options
- [Troubleshooting Guide](TROUBLESHOOTING.md) - Common issues and solutions

Enjoy coding with FastCode! 🚀
