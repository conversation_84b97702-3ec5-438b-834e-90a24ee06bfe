# FastCode User Guide

Welcome to FastCode! This guide will help you master all the features of your offline AI coding assistant.

## Quick Start

### Essential Keyboard Shortcuts
- **Ctrl+K** (Cmd+K): Inline Chat - Edit code with AI
- **Ctrl+I** (Cmd+I): Composer - Generate multiple files
- **Ctrl+L** (Cmd+L): Global Chat - Ask questions about code
- **Alt+Enter**: Auto Debug - Fix errors automatically
- **Ctrl+Enter** (Cmd+Enter): Quick chat from editor

## Core Features

### 1. Inline Chat (Ctrl+K)

Transform your code directly in the editor with AI assistance.

**How to use:**
1. Select the code you want to modify
2. Press `Ctrl+K`
3. Describe what you want to do
4. Review the diff preview
5. Choose Apply, Reject, or Retry

**Example prompts:**
- "Make this function async"
- "Add error handling"
- "Optimize for performance"
- "Add TypeScript types"
- "Convert to arrow function"
- "Add input validation"

**Tips:**
- Be specific about what you want
- Select relevant code context
- Use technical terms for better results

### 2. Composer (Ctrl+I)

Generate entire files and project structures from descriptions.

**How to use:**
1. Press `Ctrl+I` to open Composer
2. Describe what you want to build
3. Review the generated files
4. Apply all files to your workspace

**Example prompts:**
- "Create a REST API with Express, TypeScript, and Prisma"
- "Build a React component with hooks for user authentication"
- "Generate a Python Flask app with SQLAlchemy models"
- "Create a Node.js CLI tool with commander.js"

**Tips:**
- Mention specific frameworks and libraries
- Describe the project structure you want
- Include requirements like error handling, validation, etc.

### 3. Global Chat (Ctrl+L)

Ask questions about your codebase and get contextually aware answers.

**How to use:**
1. Press `Ctrl+L` to open the chat panel
2. Ask questions about your code
3. Click on file references to jump to code
4. Continue the conversation for follow-ups

**Example questions:**
- "How does user authentication work in this project?"
- "Where is the database connection configured?"
- "What are all the API endpoints in this codebase?"
- "How can I add a new feature to the user management system?"

**Tips:**
- Ask specific questions about your codebase
- Use function/class names for better context
- Follow up with clarifying questions

### 4. Auto Debug (Alt+Enter)

Automatically fix errors with AI-generated solutions.

**How to use:**
1. Place cursor on a line with an error (red squiggly)
2. Press `Alt+Enter`
3. Choose from suggested fixes
4. Apply the fix

**What it fixes:**
- Syntax errors
- Type errors
- Import/export issues
- Common logic errors
- Missing dependencies

**Tips:**
- Works best with clear error messages
- Review fixes before applying
- Use on one error at a time

## Advanced Features

### 5. Test Generation

Generate comprehensive unit tests for your code.

**How to use:**
1. Select a function, class, or code block
2. Right-click → "Generate Unit Tests"
3. Choose your testing framework
4. Review and save the generated tests

**Supported frameworks:**
- **JavaScript/TypeScript**: Jest, Mocha+Chai, Vitest
- **Python**: pytest, unittest
- **Java**: JUnit 5
- **Go**: Go testing
- **Rust**: Built-in tests

### 6. Documentation Generation

Create detailed documentation for your code.

**How to use:**
1. Select code to document
2. Right-click → "Generate Documentation"
3. Choose documentation format
4. Select placement (above, below, replace, or separate file)

**Supported formats:**
- **JavaScript/TypeScript**: JSDoc, TSDoc
- **Python**: Google Style, NumPy Style, Sphinx
- **Java**: JavaDoc
- **Go**: Go Doc
- **Rust**: Rust Doc
- **C++**: Doxygen

### 7. Terminal Commands

Convert natural language to shell commands.

**How to use:**
1. Press `Ctrl+Shift+P` → "FastCode: Terminal Command Generator"
2. Describe what you want to do
3. Choose to run, copy, or edit the command

**Example prompts:**
- "Find all JavaScript files modified in the last week"
- "Install all npm dependencies"
- "Create a new Git branch for feature development"
- "Search for TODO comments in Python files"

### 8. Semantic Search

Find code using natural language instead of exact text matching.

**How to use:**
1. Press `Ctrl+Shift+P` → "FastCode: Semantic Search"
2. Enter your search query
3. Filter by symbol type if needed
4. Click results to jump to code

**Example searches:**
- "user authentication functions"
- "database connection setup"
- "error handling middleware"
- "API route definitions"

## Best Practices

### Writing Effective Prompts

**Be Specific:**
- ❌ "Fix this code"
- ✅ "Add null checks and error handling to this function"

**Provide Context:**
- ❌ "Make it better"
- ✅ "Optimize this database query for better performance"

**Use Technical Terms:**
- ❌ "Make it work with the web"
- ✅ "Add CORS middleware for cross-origin requests"

### Code Selection Tips

**For Inline Chat:**
- Select the complete function/class/block
- Include relevant imports if needed
- Don't select too much unrelated code

**For Test Generation:**
- Select individual functions for focused tests
- Include the entire class for comprehensive testing
- Ensure all dependencies are visible

### Performance Tips

**Model Selection:**
- Use smaller models (7B) for faster responses
- Use larger models (13B+) for better quality
- Switch models based on task complexity

**Context Management:**
- Keep your workspace organized
- Use meaningful file and function names
- Add comments for complex logic

## Troubleshooting

### Common Issues

**Slow Responses:**
- Try a smaller model
- Reduce `maxTokens` setting
- Close other resource-intensive applications

**Poor Code Quality:**
- Use more specific prompts
- Provide better context
- Try a larger model

**Context Not Found:**
- Wait for indexing to complete
- Check if files are in supported languages
- Restart VSCode to reinitialize

### Getting Better Results

**For Code Generation:**
1. Be specific about requirements
2. Mention frameworks and libraries
3. Include error handling requirements
4. Specify coding style preferences

**For Code Modification:**
1. Select relevant code context
2. Use clear, actionable language
3. Mention specific patterns to follow
4. Review and iterate on results

**For Questions:**
1. Use specific function/class names
2. Ask about particular aspects
3. Follow up for clarification
4. Reference file paths when relevant

## Keyboard Shortcuts Reference

| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl+K` | Inline Chat | Edit selected code with AI |
| `Ctrl+I` | Composer | Generate multiple files |
| `Ctrl+L` | Global Chat | Ask questions about codebase |
| `Alt+Enter` | Auto Debug | Fix errors automatically |
| `Ctrl+Enter` | Quick Chat | Quick chat from editor |
| `Ctrl+Shift+V` | Apply Diff | Apply suggested changes |
| `Ctrl+Shift+R` | Retry Request | Retry last AI request |

## Tips for Different Languages

### TypeScript/JavaScript
- Mention specific frameworks (React, Vue, Express)
- Ask for type definitions
- Request modern ES6+ syntax

### Python
- Specify Python version if relevant
- Mention frameworks (Django, Flask, FastAPI)
- Ask for type hints and docstrings

### Java
- Mention Spring Boot for web applications
- Request proper exception handling
- Ask for JavaDoc documentation

### Go
- Request idiomatic Go patterns
- Ask for proper error handling
- Mention specific packages

### Rust
- Ask for memory-safe patterns
- Request proper error handling with Result
- Mention specific crates

Happy coding with FastCode! 🚀
