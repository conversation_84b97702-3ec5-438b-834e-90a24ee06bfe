{"version": 3, "file": "Constants.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/Constants.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,iCAAiC,EAAE,MAAM,+CAA+C,CAAC;AAClG,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAG9D,eAAO,MAAM,2BAA2B,qCAAqC,CAAC;AAC9E,eAAO,MAAM,+BAA+B,qBAAqB,CAAC;AAClE,eAAO,MAAM,sCAAsC,QAA0E,CAAC;AAE9H;;GAEG;AACH,eAAO,MAAM,sBAAsB;;;;;CAKzB,CAAC;AACX,MAAM,MAAM,sBAAsB,GAC9B,CAAC,OAAO,sBAAsB,CAAC,CAAC,MAAM,OAAO,sBAAsB,CAAC,CAAC;AAEzE;;GAEG;AACH,eAAO,MAAM,8BAA8B;;;;;CAKjC,CAAC;AACX,MAAM,MAAM,8BAA8B,GACtC,CAAC,OAAO,8BAA8B,CAAC,CAAC,MAAM,OAAO,8BAA8B,CAAC,CAAC;AAEzF;;GAEG;AACH,eAAO,MAAM,uCAAuC;;;;;;;;;CAS1C,CAAC;AACX,MAAM,MAAM,uCAAuC,GAC/C,CAAC,OAAO,uCAAuC,CAAC,CAAC,MAAM,OAAO,uCAAuC,CAAC,CAAC;AAE3G;;;GAGG;AACH,eAAO,MAAM,0BAA0B;;;;;;;;CAQ7B,CAAC;AACX;;;GAGG;AACH,MAAM,MAAM,0BAA0B,GAClC,CAAC,OAAO,0BAA0B,CAAC,CAAC,MAAM,OAAO,0BAA0B,CAAC,CAAC;AAEjF;;GAEG;AACH,eAAO,MAAM,qBAAqB;;;;;CAKxB,CAAC;AACX,MAAM,MAAM,qBAAqB,GAC7B,CAAC,OAAO,qBAAqB,CAAC,CAAC,MAAM,OAAO,qBAAqB,CAAC,CAAC;AAEvE;;GAEG;AACH,eAAO,MAAM,UAAU;;;CAGb,CAAC;AACX,MAAM,MAAM,UAAU,GAAG,CAAC,OAAO,UAAU,CAAC,CAAC,MAAM,OAAO,UAAU,CAAC,CAAC;AAEtE,eAAO,MAAM,WAAW;;;;;CAKd,CAAC;AACX,MAAM,MAAM,WAAW,GAAG,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;AAEzE;;GAEG;AACH,eAAO,MAAM,2BAA2B,gBAAgB,CAAC;AACzD,eAAO,MAAM,iBAAiB,sBAAsB,CAAC;AAErD;;GAEG;AACH,eAAO,MAAM,iBAAiB,KAAK,CAAC;AAEpC;;GAEG;AACH,eAAO,MAAM,IAAI;;CAEhB,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,OAAO;;CAGnB,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,KAAK;;;CAGjB,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,SAAS;;;;;;CAOrB,CAAC;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,KAAK;;;;;;;CAOjB,CAAC;AACF,MAAM,MAAM,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,MAAM,OAAO,KAAK,CAAC,CAAC;AAEvD;;GAEG;AACH,eAAO,MAAM,YAAY;;;;;;;;;;;;;CAaxB,CAAC;AAEF,eAAO,MAAM,yBAAyB;;;CAGrC,CAAC;AAEF,eAAO,MAAM,oCAAoC,EAAE,MAAa,CAAC;AAEjE,MAAM,MAAM,aAAa,GAAG,iCAAiC,GAAG,eAAe,CAAC"}