{"name": "@types/glob", "version": "8.1.0", "description": "TypeScript definitions for glob", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/glob", "license": "MIT", "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame", "githubUsername": "vvakame"}, {"name": "voy", "url": "https://github.com/voy", "githubUsername": "voy"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/glob"}, "scripts": {}, "dependencies": {"@types/minimatch": "^5.1.2", "@types/node": "*"}, "typesPublisherContentHash": "db3f962605f7f1e584202aca1e5d5827d2d161146b5f748ecb4205a455bb162e", "typeScriptVersion": "4.2"}