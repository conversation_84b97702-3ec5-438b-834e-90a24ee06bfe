Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2015
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "tree_sitter_python_binding", "tree_sitter_python_binding.vcxproj", "{A5AF2CA8-DD4A-39EE-6DB7-78F811788A11}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A5AF2CA8-DD4A-39EE-6DB7-78F811788A11}.Debug|x64.ActiveCfg = Debug|x64
		{A5AF2CA8-DD4A-39EE-6DB7-78F811788A11}.Debug|x64.Build.0 = Debug|x64
		{A5AF2CA8-DD4A-39EE-6DB7-78F811788A11}.Release|x64.ActiveCfg = Release|x64
		{A5AF2CA8-DD4A-39EE-6DB7-78F811788A11}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
