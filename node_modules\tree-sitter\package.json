{"name": "tree-sitter", "version": "0.20.6", "description": "Incremental parsers for node", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "http://github.com/tree-sitter/node-tree-sitter.git"}, "keywords": ["parser", "lexer"], "main": "index.js", "types": "tree-sitter.d.ts", "dependencies": {"nan": "^2.18.0", "prebuild-install": "^7.1.1"}, "devDependencies": {"@types/node": "^20.5.9", "chai": "^4.3.8", "jest": "^24.0.0", "mocha": "^8.4.0", "node-gyp": "^9.4.0", "prebuild": "^12.0.0", "tree-sitter-javascript": "https://github.com/tree-sitter/tree-sitter-javascript.git#master"}, "scripts": {"install": "prebuild-install || node-gyp rebuild", "build": "node-gyp build", "prebuild": "prebuild -t 10.0.0 -t 12.0.0 -t 14.0.0 -t 16.0.0 -t 18.0.0 -t 20.0.0 --strip && prebuild -r electron -t 3.0.0 -t 4.0.0 -t 5.0.0 -t 20.0.0 -t 21.0.0 -t 22.0.0 -t 23.0.0 -t 24.0.0 -t 25.0.0 --strip", "prebuild:upload": "prebuild --upload-all", "test": "mocha && jest"}}