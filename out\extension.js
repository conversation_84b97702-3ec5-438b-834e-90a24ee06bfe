"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const aiModelService_1 = require("./services/aiModelService");
const contextIndexService_1 = require("./services/contextIndexService");
const inlineChatProvider_1 = require("./providers/inlineChatProvider");
const composerProvider_1 = require("./providers/composerProvider");
const globalChatProvider_1 = require("./providers/globalChatProvider");
const autoDebugProvider_1 = require("./providers/autoDebugProvider");
const testGeneratorProvider_1 = require("./providers/testGeneratorProvider");
const docGeneratorProvider_1 = require("./providers/docGeneratorProvider");
const terminalCommandProvider_1 = require("./providers/terminalCommandProvider");
const semanticSearchProvider_1 = require("./providers/semanticSearchProvider");
let aiModelService;
let contextIndexService;
let inlineChatProvider;
let composerProvider;
let globalChatProvider;
let autoDebugProvider;
let testGeneratorProvider;
let docGeneratorProvider;
let terminalCommandProvider;
let semanticSearchProvider;
async function activate(context) {
    console.log('FastCode extension is now active!');
    // Initialize core services
    aiModelService = new aiModelService_1.AIModelService();
    contextIndexService = new contextIndexService_1.ContextIndexService(context);
    // Initialize providers
    inlineChatProvider = new inlineChatProvider_1.InlineChatProvider(aiModelService, contextIndexService);
    composerProvider = new composerProvider_1.ComposerProvider(context.extensionUri, aiModelService, contextIndexService);
    globalChatProvider = new globalChatProvider_1.GlobalChatProvider(aiModelService, contextIndexService);
    autoDebugProvider = new autoDebugProvider_1.AutoDebugProvider(aiModelService, contextIndexService);
    testGeneratorProvider = new testGeneratorProvider_1.TestGeneratorProvider(aiModelService, contextIndexService);
    docGeneratorProvider = new docGeneratorProvider_1.DocGeneratorProvider(aiModelService, contextIndexService);
    terminalCommandProvider = new terminalCommandProvider_1.TerminalCommandProvider(aiModelService);
    semanticSearchProvider = new semanticSearchProvider_1.SemanticSearchProvider(contextIndexService);
    // Register commands
    const commands = [
        vscode.commands.registerCommand('fastcode.inlineChat', () => inlineChatProvider.showInlineChat()),
        vscode.commands.registerCommand('fastcode.composer', () => composerProvider.showComposer()),
        vscode.commands.registerCommand('fastcode.globalChat', () => globalChatProvider.showGlobalChat()),
        vscode.commands.registerCommand('fastcode.autoDebug', () => autoDebugProvider.autoDebug()),
        vscode.commands.registerCommand('fastcode.generateTests', () => testGeneratorProvider.generateTests()),
        vscode.commands.registerCommand('fastcode.generateDocs', () => docGeneratorProvider.generateDocs()),
        vscode.commands.registerCommand('fastcode.terminalCommand', () => terminalCommandProvider.generateCommand()),
        vscode.commands.registerCommand('fastcode.semanticSearch', () => semanticSearchProvider.search()),
        vscode.commands.registerCommand('fastcode.applyDiff', () => inlineChatProvider.applyDiff()),
        vscode.commands.registerCommand('fastcode.retryRequest', () => inlineChatProvider.retryRequest())
    ];
    // Register providers
    context.subscriptions.push(...commands, vscode.window.registerWebviewViewProvider('fastcode.composer', composerProvider), vscode.languages.registerHoverProvider('*', autoDebugProvider));
    // Start context indexing
    await contextIndexService.initialize();
    // Show welcome message
    vscode.window.showInformationMessage('FastCode is ready! Use Ctrl+K for inline chat, Ctrl+I for composer, or Ctrl+L for global chat.');
}
exports.activate = activate;
function deactivate() {
    if (contextIndexService) {
        contextIndexService.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map