{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,8DAA2D;AAC3D,wEAAqE;AACrE,uEAAoE;AACpE,mEAAgE;AAChE,uEAAoE;AACpE,qEAAkE;AAClE,6EAA0E;AAC1E,2EAAwE;AACxE,iFAA8E;AAC9E,+EAA4E;AAE5E,IAAI,cAA8B,CAAC;AACnC,IAAI,mBAAwC,CAAC;AAC7C,IAAI,kBAAsC,CAAC;AAC3C,IAAI,gBAAkC,CAAC;AACvC,IAAI,kBAAsC,CAAC;AAC3C,IAAI,iBAAoC,CAAC;AACzC,IAAI,qBAA4C,CAAC;AACjD,IAAI,oBAA0C,CAAC;AAC/C,IAAI,uBAAgD,CAAC;AACrD,IAAI,sBAA8C,CAAC;AAE5C,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC3D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,2BAA2B;IAC3B,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;IACtC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,OAAO,CAAC,CAAC;IAEvD,uBAAuB;IACvB,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACjF,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,OAAO,CAAC,YAAY,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACnG,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACjF,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IAC/E,qBAAqB,GAAG,IAAI,6CAAqB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACvF,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACrF,uBAAuB,GAAG,IAAI,iDAAuB,CAAC,cAAc,CAAC,CAAC;IACtE,sBAAsB,GAAG,IAAI,+CAAsB,CAAC,mBAAmB,CAAC,CAAC;IAEzE,oBAAoB;IACpB,MAAM,QAAQ,GAAG;QACb,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;QACjG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;QAC3F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;QACjG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;QAC1F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE,CAAC,qBAAqB,CAAC,aAAa,EAAE,CAAC;QACtG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAAC,oBAAoB,CAAC,YAAY,EAAE,CAAC;QACnG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,eAAe,EAAE,CAAC;QAC5G,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC;QACjG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;QAC3F,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACpG,CAAC;IAEF,qBAAqB;IACrB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,GAAG,QAAQ,EACX,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,EAChF,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE,iBAAiB,CAAC,CACjE,CAAC;IAEF,yBAAyB;IACzB,MAAM,mBAAmB,CAAC,UAAU,EAAE,CAAC;IAEvC,uBAAuB;IACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,gGAAgG,CAAC,CAAC;AAC3I,CAAC;AA3CD,4BA2CC;AAED,SAAgB,UAAU;IACtB,IAAI,mBAAmB,EAAE;QACrB,mBAAmB,CAAC,OAAO,EAAE,CAAC;KACjC;AACL,CAAC;AAJD,gCAIC"}