{"version": 3, "file": "autoDebugProvider.js", "sourceRoot": "", "sources": ["../../src/providers/autoDebugProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAmBjC,MAAa,iBAAiB;IAC1B,YACY,SAAyB,EACzB,cAAmC;QADnC,cAAS,GAAT,SAAS,CAAgB;QACzB,mBAAc,GAAd,cAAc,CAAqB;IAC5C,CAAC;IAEJ,KAAK,CAAC,SAAS;QACX,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;QACzC,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEzE,mCAAmC;QACnC,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAC/C,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAChD,CAAC;QAEF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,CAAC;YACxE,OAAO;SACV;QAED,sCAAsC;QACtC,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACrD,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CACpD,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAChD,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,MAAyB,EAAE,UAA6B;QAC7E,MAAM,SAAS,GAAc;YACzB,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;YACrC,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS;YACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,SAAS;YACtC,IAAI,EAAE,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI;SACtF,CAAC;QAEF,IAAI;YACA,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,8BAA8B;gBACrC,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAEzE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxB,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;iBACzD;qBAAM;oBACH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6CAA6C,CAAC,CAAC;iBACvF;YACL,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAyB,EAAE,SAAoB;QAChF,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEtD,+BAA+B;QAC/B,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CACjC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,EAChC,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,EACpD,CAAC,CACJ,CAAC;QACF,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEnD,uBAAuB;QACvB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CACnE,QAAQ,CAAC,QAAQ,EACjB,SAAS,CAAC,IAAI,EACd,SAAS,CAAC,MAAM,CACnB,CAAC;QAEF,mBAAmB;QACnB,MAAM,SAAS,GAAc;YACzB,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,eAAe,CAAC;YACtF,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACnB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAEO,gBAAgB,CAAC,SAAoB,EAAE,SAAiB,EAAE,OAAe,EAAE,eAAuB;QACtG,OAAO;;;aAGF,SAAS,CAAC,OAAO;UACpB,SAAS,CAAC,IAAI;YACZ,SAAS,CAAC,MAAM;YAChB,SAAS,CAAC,MAAM;UAClB,SAAS,CAAC,IAAI,IAAI,KAAK;;;;EAI/B,SAAS;;;;;EAKT,OAAO;;;EAGP,eAAe,CAAC,CAAC,CAAC,sBAAsB,eAAe,IAAI,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;sGAkBoC,CAAC;IACnG,CAAC;IAEO,mBAAmB,CAAC,QAAgB,EAAE,SAAoB,EAAE,QAA6B;QAC7F,IAAI;YACA,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,SAAS,EAAE;gBACZ,OAAO,EAAE,CAAC;aACb;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC/C,OAAO,EAAE,CAAC;aACb;YAED,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE;gBACjC,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,KAAK,CAC/B,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,EACrB,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CACtE,CAAC;gBAEF,OAAO;oBACH,WAAW,EAAE,GAAG,CAAC,WAAW,IAAI,oBAAoB;oBACpD,OAAO,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;oBACjE,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;iBAC7D,CAAC;YACN,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,UAAyB,EAAE,EAAE,CACpC,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CACxE,CAAC;SAEL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAyB,EAAE,WAA4B;QACvF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,oCAAoC;YACpC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,sBAAsB,UAAU,CAAC,WAAW,iBAAiB,UAAU,CAAC,UAAU,MAAM,EACxF,WAAW,EACX,QAAQ,CACX,CAAC;YAEF,IAAI,MAAM,KAAK,WAAW,EAAE;gBACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aAC3C;SACJ;aAAM;YACH,yCAAyC;YACzC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBACzC,KAAK,EAAE,UAAU,CAAC,WAAW;gBAC7B,MAAM,EAAE,eAAe,UAAU,CAAC,UAAU,KAAK;gBACjD,UAAU;aACb,CAAC,CAAC,CAAC;YAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;gBACtD,KAAK,EAAE,wBAAwB;gBAC/B,WAAW,EAAE,oCAAoC;aACpD,CAAC,CAAC;YAEH,IAAI,QAAQ,EAAE;gBACV,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;aACpD;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAAyB;QACvE,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC5C,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE;oBACrC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;iBACrD;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;aAC9E;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;aACzD;SACJ;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAC3E;IACL,CAAC;IAED,gCAAgC;IAChC,KAAK,CAAC,YAAY,CACd,QAA6B,EAC7B,QAAyB,EACzB,KAA+B;QAE/B,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAC/C,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACtC,CAAC;QAEF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YAC9C,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,MAAM,IAAI,OAAO,OAAO,UAAU,CAAC,OAAO,EAAE,CAAC;YAE3E,IAAI,UAAU,CAAC,IAAI,EAAE;gBACjB,OAAO,IAAI,KAAK,UAAU,CAAC,IAAI,GAAG,CAAC;aACtC;YAED,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QACjD,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QACxD,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC3C,YAAY,CAAC,cAAc,CAAC,wDAAwD,CAAC,CAAC;QAEtF,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;CACJ;AA1PD,8CA0PC"}