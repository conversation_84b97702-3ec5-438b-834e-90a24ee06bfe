{"version": 3, "file": "composerProvider.js", "sourceRoot": "", "sources": ["../../src/providers/composerProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AAgBzB,MAAa,gBAAgB;IAKzB,YACqB,aAAyB,EAClC,SAAyB,EACzB,cAAmC;QAF1B,kBAAa,GAAb,aAAa,CAAY;QAClC,cAAS,GAAT,SAAS,CAAgB;QACzB,mBAAc,GAAd,cAAc,CAAqB;QALvC,mBAAc,GAA2B,IAAI,CAAC;IAMnD,CAAC;IAEG,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;SAC3C,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACnD,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACf,KAAK,UAAU;oBACX,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvC,MAAM;gBACV,KAAK,OAAO;oBACR,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAClC,MAAM;gBACV,KAAK,QAAQ;oBACT,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,MAAM;gBACV,KAAK,OAAO;oBACR,IAAI,IAAI,CAAC,cAAc,EAAE;wBACrB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;qBACzD;oBACD,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,YAAY;QACrB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;SAC3B;aAAM;YACH,uDAAuD;YACvD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7C,KAAK,EAAE,mBAAmB;gBAC1B,MAAM,EAAE,yCAAyC;gBACjD,WAAW,EAAE,gEAAgE;aAChF,CAAC,CAAC;YAEH,IAAI,OAAO,EAAE;gBACT,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;aACrC;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAe;QACvC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG;YAClB,OAAO;YACP,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,YAAY;SACvB,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI;YACA,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,+BAA+B;gBACtC,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAEtD,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;oBAClC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,WAAW,CAAC;oBACzC,IAAI,CAAC,aAAa,EAAE,CAAC;iBACxB;YACL,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;gBACpC,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAe;QAC7C,wBAAwB;QACxB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE1D,MAAM,SAAS,GAAc;YACzB,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,CAAC;YAC3D,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACnB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,OAAO,+BAA+B,CAAC;SAC1C;QAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QAChD,IAAI,OAAO,GAAG,cAAc,QAAQ,MAAM,CAAC;QAE3C,gCAAgC;QAChC,MAAM,WAAW,GAAG;YAChB,cAAc;YACd,eAAe;YACf,kBAAkB;YAClB,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,cAAc;SACjB,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACnD,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBAC3B,IAAI;oBACA,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;oBACpD,OAAO,IAAI,GAAG,UAAU,kBAAkB,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC;iBACtF;gBAAC,OAAO,KAAK,EAAE;oBACZ,qBAAqB;iBACxB;aACJ;SACJ;QAED,0BAA0B;QAC1B,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAChE,OAAO,IAAI,yBAAyB,SAAS,MAAM,CAAC;SACvD;QAAC,OAAO,KAAK,EAAE;YACZ,gBAAgB;SACnB;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,OAAe,EAAE,QAAgB,EAAE,YAAY,GAAG,CAAC;QACnF,IAAI,YAAY,IAAI,QAAQ,EAAE;YAC1B,OAAO,EAAE,CAAC;SACb;QAED,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAEzC,IAAI;YACA,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEtC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,cAAc,EAAE;oBACjD,SAAS;iBACZ;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC1C,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;oBACpB,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,CAAC;oBACnC,SAAS,IAAI,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;iBACvF;qBAAM;oBACH,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,CAAC;iBACrC;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,gBAAgB;SACnB;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,mBAAmB,CAAC,OAAe,EAAE,gBAAwB;QACjE,OAAO;;EAEb,gBAAgB;;gBAEF,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;qFA0B8D,CAAC;IAClF,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC3C,IAAI;YACA,wCAAwC;YACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,SAAS,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAChD;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC/C,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC9C;YAED,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACpC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;aACtC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;SAElE;QAAC,OAAO,KAAK,EAAE;YACZ,0DAA0D;YAC1D,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;SAChD;IACL,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC3C,MAAM,KAAK,GAAoB,EAAE,CAAC;QAClC,MAAM,SAAS,GAAG,sFAAsF,CAAC;QAEzG,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE;YAChD,KAAK,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;gBACrB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;gBACxB,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,gBAAgB;aACpD,CAAC,CAAC;SACN;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,KAAsB;QAC3C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO;SACV;QAED,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAC5D,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QAChD,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,IAAI;YACA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEnC,uCAAuC;gBACvC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBACrB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;iBAC1C;gBAED,aAAa;gBACb,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACjD,YAAY,EAAE,CAAC;aAClB;YAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,YAAY,qBAAqB,CAAC,CAAC;YAE7F,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,UAAU,CAAC;gBACxC,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;YAED,sBAAsB;YACtB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACzD,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC3C,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;aAC7C;SAEJ;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9E;IACL,CAAC;IAEO,WAAW;QACf,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI,CAAC,cAAc;aAC/B,CAAC,CAAC;SACN;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAgNP,CAAC;IACL,CAAC;;AA3hBL,4CA4hBC;AA3hB0B,yBAAQ,GAAG,mBAAmB,CAAC"}