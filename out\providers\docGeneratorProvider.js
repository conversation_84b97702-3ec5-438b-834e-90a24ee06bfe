"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocGeneratorProvider = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class DocGeneratorProvider {
    constructor(aiService, contextService) {
        this.aiService = aiService;
        this.contextService = contextService;
        this.docFormats = new Map();
        this.initializeDocFormats();
    }
    initializeDocFormats() {
        // JavaScript/TypeScript documentation
        this.docFormats.set('javascript', [
            {
                name: 'JSDoc',
                extension: '.js',
                template: 'jsdoc'
            }
        ]);
        this.docFormats.set('typescript', [
            {
                name: 'TSDoc',
                extension: '.ts',
                template: 'tsdoc'
            }
        ]);
        // Python documentation
        this.docFormats.set('python', [
            {
                name: 'Google Style',
                extension: '.py',
                template: 'google'
            },
            {
                name: 'NumPy Style',
                extension: '.py',
                template: 'numpy'
            },
            {
                name: 'Sphinx Style',
                extension: '.py',
                template: 'sphinx'
            }
        ]);
        // Java documentation
        this.docFormats.set('java', [
            {
                name: 'JavaDoc',
                extension: '.java',
                template: 'javadoc'
            }
        ]);
        // Go documentation
        this.docFormats.set('go', [
            {
                name: 'Go Doc',
                extension: '.go',
                template: 'godoc'
            }
        ]);
        // Rust documentation
        this.docFormats.set('rust', [
            {
                name: 'Rust Doc',
                extension: '.rs',
                template: 'rustdoc'
            }
        ]);
        // C++ documentation
        this.docFormats.set('cpp', [
            {
                name: 'Doxygen',
                extension: '.cpp',
                template: 'doxygen'
            }
        ]);
    }
    async generateDocs() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select the code you want to document');
            return;
        }
        const selectedCode = editor.document.getText(selection);
        const language = editor.document.languageId;
        const filePath = editor.document.fileName;
        // Get available documentation formats for this language
        const formats = this.docFormats.get(language);
        if (!formats || formats.length === 0) {
            vscode.window.showErrorMessage(`Documentation generation not supported for ${language}`);
            return;
        }
        // Let user choose format if multiple options
        let selectedFormat;
        if (formats.length === 1) {
            selectedFormat = formats[0];
        }
        else {
            const formatItems = formats.map(fmt => ({
                label: fmt.name,
                format: fmt
            }));
            const selected = await vscode.window.showQuickPick(formatItems, {
                title: 'Select Documentation Format',
                placeHolder: 'Choose the documentation style to use'
            });
            if (!selected) {
                return;
            }
            selectedFormat = selected.format;
        }
        // Ask user where to place the documentation
        const placement = await vscode.window.showQuickPick([
            { label: 'Above the code', value: 'above' },
            { label: 'Below the code', value: 'below' },
            { label: 'Replace selection', value: 'replace' },
            { label: 'Separate file', value: 'file' }
        ], {
            title: 'Where to place documentation?',
            placeHolder: 'Choose where to insert the generated documentation'
        });
        if (!placement) {
            return;
        }
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Generating documentation...',
                cancellable: false
            }, async () => {
                const documentation = await this.generateDocumentation(selectedCode, language, filePath, selectedFormat);
                await this.insertDocumentation(editor, selection, documentation, placement.value, selectedFormat);
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
        }
    }
    async generateDocumentation(selectedCode, language, filePath, format) {
        // Get context around the selected code
        const context = await this.contextService.getContextForPosition(filePath, 0, 0);
        const aiRequest = {
            prompt: this.buildDocPrompt(selectedCode, language, context, format),
            maxTokens: 1024,
            temperature: 0.2
        };
        const response = await this.aiService.generateCompletion(aiRequest);
        return this.extractDocumentation(response.content);
    }
    buildDocPrompt(selectedCode, language, context, format) {
        const examples = this.getDocumentationExamples(format.template);
        return `You are a documentation generation assistant. Generate comprehensive documentation for the given code.

Language: ${language}
Documentation Format: ${format.name}

Code to document:
\`\`\`${language}
${selectedCode}
\`\`\`

${context ? `Context:\n${context}\n` : ''}

Requirements:
1. Use ${format.name} documentation format
2. Include:
   - Clear description of what the code does
   - Parameter descriptions (if applicable)
   - Return value description (if applicable)
   - Usage examples (if helpful)
   - Any important notes or warnings
3. Be concise but comprehensive
4. Use proper formatting for ${format.name}

${examples ? `Example format:\n${examples}\n` : ''}

Generate only the documentation comments, without the original code.`;
    }
    getDocumentationExamples(template) {
        const examples = {
            'jsdoc': `/**
 * Calculates the sum of two numbers.
 * @param {number} a - The first number.
 * @param {number} b - The second number.
 * @returns {number} The sum of a and b.
 * @example
 * const result = add(5, 3); // returns 8
 */`,
            'tsdoc': `/**
 * Calculates the sum of two numbers.
 * @param a - The first number.
 * @param b - The second number.
 * @returns The sum of a and b.
 * @example
 * \`\`\`typescript
 * const result = add(5, 3); // returns 8
 * \`\`\`
 */`,
            'google': `"""Calculates the sum of two numbers.

Args:
    a (int): The first number.
    b (int): The second number.

Returns:
    int: The sum of a and b.

Example:
    >>> add(5, 3)
    8
"""`,
            'numpy': `"""Calculates the sum of two numbers.

Parameters
----------
a : int
    The first number.
b : int
    The second number.

Returns
-------
int
    The sum of a and b.

Examples
--------
>>> add(5, 3)
8
"""`,
            'sphinx': `"""Calculates the sum of two numbers.

:param a: The first number.
:type a: int
:param b: The second number.
:type b: int
:returns: The sum of a and b.
:rtype: int

Example:
    >>> add(5, 3)
    8
"""`,
            'javadoc': `/**
 * Calculates the sum of two numbers.
 * @param a The first number.
 * @param b The second number.
 * @return The sum of a and b.
 * @since 1.0
 */`,
            'godoc': `// Add calculates the sum of two numbers.
// It takes two integers and returns their sum.
//
// Example:
//   result := Add(5, 3) // returns 8`,
            'rustdoc': `/// Calculates the sum of two numbers.
///
/// # Arguments
///
/// * \`a\` - The first number.
/// * \`b\` - The second number.
///
/// # Returns
///
/// The sum of a and b.
///
/// # Examples
///
/// \`\`\`
/// let result = add(5, 3);
/// assert_eq!(result, 8);
/// \`\`\``,
            'doxygen': `/**
 * @brief Calculates the sum of two numbers.
 * @param a The first number.
 * @param b The second number.
 * @return The sum of a and b.
 * 
 * @code
 * int result = add(5, 3); // returns 8
 * @endcode
 */`
        };
        return examples[template] || '';
    }
    extractDocumentation(response) {
        // Remove markdown code blocks if present
        const codeBlockRegex = /```[\w]*\n?([\s\S]*?)\n?```/;
        const match = response.match(codeBlockRegex);
        if (match) {
            return match[1].trim();
        }
        return response.trim();
    }
    async insertDocumentation(editor, selection, documentation, placement, format) {
        switch (placement) {
            case 'above':
                await this.insertAbove(editor, selection, documentation);
                break;
            case 'below':
                await this.insertBelow(editor, selection, documentation);
                break;
            case 'replace':
                await this.replaceSelection(editor, selection, documentation);
                break;
            case 'file':
                await this.createDocFile(editor.document.fileName, documentation, format);
                break;
        }
    }
    async insertAbove(editor, selection, documentation) {
        const insertPosition = new vscode.Position(selection.start.line, 0);
        const indentation = this.getIndentation(editor.document, selection.start.line);
        const indentedDoc = this.indentDocumentation(documentation, indentation);
        await editor.edit(editBuilder => {
            editBuilder.insert(insertPosition, indentedDoc + '\n');
        });
        vscode.window.showInformationMessage('Documentation inserted above the code');
    }
    async insertBelow(editor, selection, documentation) {
        const insertPosition = new vscode.Position(selection.end.line + 1, 0);
        const indentation = this.getIndentation(editor.document, selection.start.line);
        const indentedDoc = this.indentDocumentation(documentation, indentation);
        await editor.edit(editBuilder => {
            editBuilder.insert(insertPosition, indentedDoc + '\n');
        });
        vscode.window.showInformationMessage('Documentation inserted below the code');
    }
    async replaceSelection(editor, selection, documentation) {
        const indentation = this.getIndentation(editor.document, selection.start.line);
        const indentedDoc = this.indentDocumentation(documentation, indentation);
        await editor.edit(editBuilder => {
            editBuilder.replace(selection, indentedDoc);
        });
        vscode.window.showInformationMessage('Selection replaced with documentation');
    }
    async createDocFile(originalFilePath, documentation, format) {
        const dir = path.dirname(originalFilePath);
        const baseName = path.basename(originalFilePath, path.extname(originalFilePath));
        const docFileName = `${baseName}_docs.md`;
        const docFilePath = path.join(dir, docFileName);
        const docContent = `# Documentation for ${baseName}\n\n${documentation}`;
        const uri = vscode.Uri.file(docFilePath);
        const doc = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(doc);
        await editor.edit(editBuilder => {
            editBuilder.insert(new vscode.Position(0, 0), docContent);
        });
        vscode.window.showInformationMessage(`Documentation file created: ${docFileName}`);
    }
    getIndentation(document, lineNumber) {
        const line = document.lineAt(lineNumber);
        const match = line.text.match(/^(\s*)/);
        return match ? match[1] : '';
    }
    indentDocumentation(documentation, indentation) {
        return documentation
            .split('\n')
            .map(line => line ? indentation + line : line)
            .join('\n');
    }
}
exports.DocGeneratorProvider = DocGeneratorProvider;
//# sourceMappingURL=docGeneratorProvider.js.map