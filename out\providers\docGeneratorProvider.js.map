{"version": 3, "file": "docGeneratorProvider.js", "sourceRoot": "", "sources": ["../../src/providers/docGeneratorProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAU7B,MAAa,oBAAoB;IAG7B,YACY,SAAyB,EACzB,cAAmC;QADnC,cAAS,GAAT,SAAS,CAAgB;QACzB,mBAAc,GAAd,cAAc,CAAqB;QAJvC,eAAU,GAA6B,IAAI,GAAG,EAAE,CAAC;QAMrD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,sCAAsC;QACtC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE;YAC9B;gBACI,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,OAAO;aACpB;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE;YAC9B;gBACI,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,OAAO;aACpB;SACJ,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC1B;gBACI,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,QAAQ;aACrB;YACD;gBACI,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,OAAO;aACpB;YACD;gBACI,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,QAAQ;aACrB;SACJ,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE;YACxB;gBACI,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,SAAS;aACtB;SACJ,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE;YACtB;gBACI,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,OAAO;aACpB;SACJ,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE;YACxB;gBACI,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,SAAS;aACtB;SACJ,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE;YACvB;gBACI,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,SAAS;aACtB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,YAAY;QACd,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE;YACnB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6CAA6C,CAAC,CAAC;YAC9E,OAAO;SACV;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAE1C,wDAAwD;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8CAA8C,QAAQ,EAAE,CAAC,CAAC;YACzF,OAAO;SACV;QAED,6CAA6C;QAC7C,IAAI,cAAyB,CAAC;QAC9B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;SAC/B;aAAM;YACH,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACpC,KAAK,EAAE,GAAG,CAAC,IAAI;gBACf,MAAM,EAAE,GAAG;aACd,CAAC,CAAC,CAAC;YAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE;gBAC5D,KAAK,EAAE,6BAA6B;gBACpC,WAAW,EAAE,uCAAuC;aACvD,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE;gBACX,OAAO;aACV;YAED,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;SACpC;QAED,4CAA4C;QAC5C,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;YAChD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE;YAC3C,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE;YAC3C,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE;YAChD,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE;SAC5C,EAAE;YACC,KAAK,EAAE,+BAA+B;YACtC,WAAW,EAAE,oDAAoD;SACpE,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QAED,IAAI;YACA,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,uCAAuC;gBAC9C,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAClD,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,cAAc,CACjB,CAAC;gBAEF,MAAM,IAAI,CAAC,mBAAmB,CAC1B,MAAM,EACN,SAAS,EACT,aAAa,EACb,SAAS,CAAC,KAAK,EACf,cAAc,CACjB,CAAC;YACN,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAC/B,YAAoB,EACpB,QAAgB,EAChB,QAAgB,EAChB,MAAiB;QAEjB,uCAAuC;QACvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAC3D,QAAQ,EACR,CAAC,EACD,CAAC,CACJ,CAAC;QAEF,MAAM,SAAS,GAAc;YACzB,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;YACpE,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACnB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;IAEO,cAAc,CAClB,YAAoB,EACpB,QAAgB,EAChB,OAAe,EACf,MAAiB;QAEjB,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEhE,OAAO;;YAEH,QAAQ;wBACI,MAAM,CAAC,IAAI;;;QAG3B,QAAQ;EACd,YAAY;;;EAGZ,OAAO,CAAC,CAAC,CAAC,aAAa,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE;;;SAGhC,MAAM,CAAC,IAAI;;;;;;;;+BAQW,MAAM,CAAC,IAAI;;EAExC,QAAQ,CAAC,CAAC,CAAC,oBAAoB,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE;;qEAEmB,CAAC;IAClE,CAAC;IAEO,wBAAwB,CAAC,QAAgB;QAC7C,MAAM,QAAQ,GAA8B;YACxC,OAAO,EAAE;;;;;;;IAOjB;YACQ,OAAO,EAAE;;;;;;;;;IASjB;YACQ,QAAQ,EAAE;;;;;;;;;;;;IAYlB;YACQ,OAAO,EAAE;;;;;;;;;;;;;;;;;;IAkBjB;YACQ,QAAQ,EAAE;;;;;;;;;;;;IAYlB;YACQ,SAAS,EAAE;;;;;;IAMnB;YACQ,OAAO,EAAE;;;;sCAIiB;YAC1B,SAAS,EAAE;;;;;;;;;;;;;;;;WAgBZ;YACC,SAAS,EAAE;;;;;;;;;IASnB;SACK,CAAC;QAEF,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACpC,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QACzC,yCAAyC;QACzC,MAAM,cAAc,GAAG,6BAA6B,CAAC;QACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SAC1B;QAED,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC7B,MAAyB,EACzB,SAA2B,EAC3B,aAAqB,EACrB,SAAiB,EACjB,MAAiB;QAEjB,QAAQ,SAAS,EAAE;YACf,KAAK,OAAO;gBACR,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBAC9D,MAAM;YACV,KAAK,MAAM;gBACP,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;gBAC1E,MAAM;SACb;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAyB,EAAE,SAA2B,EAAE,aAAqB;QACnG,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEzE,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC5B,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;IAClF,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAyB,EAAE,SAA2B,EAAE,aAAqB;QACnG,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEzE,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC5B,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;IAClF,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAyB,EAAE,SAA2B,EAAE,aAAqB;QACxG,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEzE,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC5B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;IAClF,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,gBAAwB,EAAE,aAAqB,EAAE,MAAiB;QAC1F,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACjF,MAAM,WAAW,GAAG,GAAG,QAAQ,UAAU,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAEhD,MAAM,UAAU,GAAG,uBAAuB,QAAQ,OAAO,aAAa,EAAE,CAAC;QAEzE,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAEzD,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC5B,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;IACvF,CAAC;IAEO,cAAc,CAAC,QAA6B,EAAE,UAAkB;QACpE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjC,CAAC;IAEO,mBAAmB,CAAC,aAAqB,EAAE,WAAmB;QAClE,OAAO,aAAa;aACf,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;aAC7C,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;CACJ;AAxbD,oDAwbC"}