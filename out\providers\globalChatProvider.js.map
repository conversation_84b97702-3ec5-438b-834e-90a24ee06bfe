{"version": 3, "file": "globalChatProvider.js", "sourceRoot": "", "sources": ["../../src/providers/globalChatProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAgBjC,MAAa,kBAAkB;IAO3B,YACY,SAAyB,EACzB,cAAmC;QADnC,cAAS,GAAT,SAAS,CAAgB;QACzB,mBAAc,GAAd,cAAc,CAAqB;QARvC,cAAS,GAA+B,IAAI,CAAC;QAC7C,mBAAc,GAAgB;YAClC,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,KAAK;SAClB,CAAC;IAKC,CAAC;IAEJ,KAAK,CAAC,cAAc;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC7C,oBAAoB,EACpB,eAAe,EACf,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEvD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACzD,QAAQ,OAAO,CAAC,IAAI,EAAE;gBAClB,KAAK,aAAa;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAC9C,MAAM;gBACV,KAAK,WAAW;oBACZ,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,MAAM;gBACV,KAAK,UAAU;oBACX,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBACpD,MAAM;aACb;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC3C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;YACjB,OAAO;SACV;QAED,mBAAmB;QACnB,MAAM,WAAW,GAAgB;YAC7B,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI;YACA,mCAAmC;YACnC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAEhE,oCAAoC;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAE5D,uBAAuB;YACvB,MAAM,SAAS,GAAc;gBACzB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC;gBAC9C,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,GAAG;aACnB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAEpE,wCAAwC;YACxC,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAE/E,wBAAwB;YACxB,MAAM,gBAAgB,GAAgB;gBAClC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU;aACb,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpD,IAAI,CAAC,aAAa,EAAE,CAAC;SAExB;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,YAAY,GAAgB;gBAC9B,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,kCAAkC,KAAK,CAAC,OAAO,EAAE;gBAC1D,SAAS,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAa;QAC7C,gDAAgD;QAChD,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC;QAEvE,IAAI,UAAU,GAAmB,EAAE,CAAC;QAEpC,mCAAmC;QACnC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE;YAChC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,0BAA0B;gBAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAChE,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC3C;SACJ;QAED,0CAA0C;QAC1C,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAC5D,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,QAAQ;YAC7C,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CACrE,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC;IAEO,uBAAuB,CAAC,OAAuB;QACnD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,EAAE,CAAC;SACb;QAED,IAAI,OAAO,GAAG,4BAA4B,CAAC;QAE3C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YAC7B,OAAO,IAAI,SAAS,MAAM,CAAC,QAAQ,IAAI,CAAC;YACxC,OAAO,IAAI,WAAW,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC;YAEvD,IAAI,MAAM,CAAC,SAAS,EAAE;gBAClB,OAAO,IAAI,cAAc,MAAM,CAAC,SAAS,IAAI,CAAC;aACjD;YAED,IAAI,MAAM,CAAC,SAAS,EAAE;gBAClB,OAAO,IAAI,kBAAkB,MAAM,CAAC,SAAS,IAAI,CAAC;aACrD;YAED,OAAO,IAAI,kBAAkB,MAAM,CAAC,OAAO,cAAc,CAAC;SAC7D;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,eAAe,CAAC,WAAmB,EAAE,OAAe;QACxD,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;aACnD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;aACxC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;aACzC,IAAI,CAAC,MAAM,CAAC,CAAC;QAElB,OAAO;;EAEb,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE;;;EAG7B,mBAAmB;;QAEb,WAAW;;yJAEsI,CAAC;IACtJ,CAAC;IAEO,qBAAqB,CAAC,OAAe,EAAE,aAA6B;QACxE,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,+CAA+C;QAC/C,MAAM,aAAa,GAAG,6DAA6D,CAAC;QACpF,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAE7C,IAAI,OAAO,EAAE;YACT,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;SAC/B;QAED,4DAA4D;QAC5D,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE;YAChC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aAC3C;SACJ;QAED,oBAAoB;QACpB,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IACpC,CAAC;IAEO,SAAS;QACb,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,IAAa;QAClD,IAAI;YACA,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE9D,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE;gBAClB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC5D,MAAM,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;aAC5D;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;aACzC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAsPP,CAAC;IACL,CAAC;CACJ;AA9dD,gDA8dC"}