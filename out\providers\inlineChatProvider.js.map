{"version": 3, "file": "inlineChatProvider.js", "sourceRoot": "", "sources": ["../../src/providers/inlineChatProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,2CAA6B;AAY7B,MAAa,kBAAkB;IAI3B,YACY,SAAyB,EACzB,cAAmC;QADnC,cAAS,GAAT,SAAS,CAAgB;QACzB,mBAAc,GAAd,cAAc,CAAqB;QALvC,mBAAc,GAA6B,IAAI,CAAC;QAChD,gBAAW,GAAkB,IAAI,CAAC;IAKvC,CAAC;IAEJ,KAAK,CAAC,cAAc;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,gCAAgC;QAChC,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAExD,mBAAmB;QACnB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAChD,QAAQ,CAAC,KAAK,GAAG,sBAAsB,CAAC;QACxC,QAAQ,CAAC,WAAW,GAAG,oEAAoE,CAAC;QAC5F,QAAQ,CAAC,MAAM,GAAG,sDAAsD,CAAC;QAEzE,qCAAqC;QACrC,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;YAChE,eAAe,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,qCAAqC,CAAC;YAC7E,MAAM,EAAE,WAAW;YACnB,WAAW,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,wBAAwB,CAAC;SAC/D,CAAC,CAAC;QAEH,0BAA0B;QAC1B,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;QAEnD,iBAAiB;QACjB,IAAI,CAAC,cAAc,GAAG;YAClB,MAAM;YACN,SAAS;YACT,YAAY;YACZ,QAAQ;YACR,cAAc;SACjB,CAAC;QAEF,eAAe;QACf,QAAQ,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,EAAE;gBACV,OAAO;aACV;YAED,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;QAChE,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC;QAE1C,IAAI;YACA,gBAAgB;YAChB,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,iCAAiC;gBACxC,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,cAAc;gBACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAE3D,mBAAmB;gBACnB,MAAM,SAAS,GAAc;oBACzB,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC;oBAC9D,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,GAAG;iBACnB,CAAC;gBAEF,kBAAkB;gBAClB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBACpE,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAE/D,IAAI,OAAO,IAAI,OAAO,KAAK,YAAY,EAAE;oBACrC,IAAI,CAAC,cAAe,CAAC,YAAY,GAAG,OAAO,CAAC;oBAC5C,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;iBACrD;qBAAM;oBACH,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;oBAC/D,IAAI,CAAC,cAAc,EAAE,CAAC;iBACzB;YACL,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAyB,EAAE,SAA2B;QAC7E,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;QAEzC,0BAA0B;QAC1B,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CACjC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EACtC,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAChE,CAAC,CACJ,CAAC;QACF,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAE9D,kCAAkC;QAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAEhG,OAAO,SAAS,QAAQ;YACpB,MAAM,CAAC,QAAQ,CAAC,UAAU;;;QAG9B,MAAM,CAAC,QAAQ,CAAC,UAAU;EAChC,eAAe;;;EAGf,eAAe,CAAC,CAAC,CAAC,sBAAsB,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC/D,CAAC;IAEO,iBAAiB,CAAC,OAAe,EAAE,YAAoB,EAAE,OAAe;QAC5E,OAAO;;EAEb,OAAO;;;;EAIP,YAAY;;;gBAGE,OAAO;;iLAE0J,CAAC;IAC9K,CAAC;IAEO,uBAAuB,CAAC,QAAgB;QAC5C,yCAAyC;QACzC,MAAM,cAAc,GAAG,6BAA6B,CAAC;QACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SAC1B;QAED,yDAAyD;QACzD,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,YAAoB,EAAE,OAAe;QAC/D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAE3B,mBAAmB;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAE5D,8BAA8B;QAC9B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACpD,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE;YAC1C,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM;YACpC,OAAO,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,8BAA8B,EAC9B,EAAE,KAAK,EAAE,KAAK,EAAE,EAChB,OAAO,EACP,QAAQ,EACR,OAAO,CACV,CAAC;QAEF,QAAQ,MAAM,EAAE;YACZ,KAAK,OAAO;gBACR,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvB,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC1B,MAAM;YACV;gBACI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;SACb;IACL,CAAC;IAEO,cAAc,CAAC,YAAoB,EAAE,OAAe;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACxF,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,SAAS;QACX,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC3C,OAAO;SACV;QAED,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;QAElD,IAAI;YACA,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC5B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,WAAY,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;SAClF;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAC/E;gBAAS;YACN,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IAED,KAAK,CAAC,YAAY;QACd,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;YAC1D,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAChD,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEO,cAAc;QAClB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,qBAAqB;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;YAE7C,kCAAkC;YAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,6BAA6B;QAC7B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;IACzE,CAAC;CACJ;AAtQD,gDAsQC"}