"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SemanticSearchProvider = void 0;
const vscode = __importStar(require("vscode"));
class SemanticSearchProvider {
    constructor(contextService) {
        this.contextService = contextService;
        this.searchPanel = null;
    }
    async search() {
        if (this.searchPanel) {
            this.searchPanel.reveal();
            return;
        }
        this.searchPanel = vscode.window.createWebviewPanel('fastcodeSemanticSearch', 'FastCode Semantic Search', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true
        });
        this.searchPanel.webview.html = this.getWebviewContent();
        this.searchPanel.webview.onDidReceiveMessage(async (message) => {
            switch (message.type) {
                case 'search':
                    await this.performSearch(message.query, message.type);
                    break;
                case 'openFile':
                    await this.openFile(message.filePath, message.line);
                    break;
            }
        });
        this.searchPanel.onDidDispose(() => {
            this.searchPanel = null;
        });
    }
    async performSearch(query, searchType) {
        if (!query.trim()) {
            return;
        }
        try {
            const results = await this.contextService.searchSymbols(query, searchType);
            this.searchPanel?.webview.postMessage({
                type: 'searchResults',
                query,
                results: results.map(result => ({
                    symbol: result.symbol,
                    score: result.score,
                    relevantContext: result.relevantContext
                }))
            });
        }
        catch (error) {
            this.searchPanel?.webview.postMessage({
                type: 'error',
                message: error.message
            });
        }
    }
    async openFile(filePath, line) {
        try {
            const uri = vscode.Uri.file(filePath);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document);
            if (line && line > 0) {
                const position = new vscode.Position(line - 1, 0);
                editor.selection = new vscode.Selection(position, position);
                editor.revealRange(new vscode.Range(position, position));
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Could not open file: ${filePath}`);
        }
    }
    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastCode Semantic Search</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        .search-input {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        #searchQuery {
            flex: 1;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            padding: 8px;
            border-radius: 4px;
            font-family: inherit;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .filter-options {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .filter-option {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .filter-option input[type="radio"] {
            margin: 0;
        }
        .results-container {
            margin-top: 20px;
        }
        .result-item {
            border: 1px solid var(--vscode-panel-border);
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 4px;
            background-color: var(--vscode-textCodeBlock-background);
        }
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .symbol-name {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
            cursor: pointer;
        }
        .symbol-name:hover {
            text-decoration: underline;
        }
        .symbol-type {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .symbol-score {
            color: var(--vscode-descriptionForeground);
            font-size: 0.9em;
        }
        .file-path {
            color: var(--vscode-descriptionForeground);
            font-size: 0.9em;
            margin-bottom: 5px;
            cursor: pointer;
        }
        .file-path:hover {
            color: var(--vscode-textLink-foreground);
        }
        .symbol-signature {
            font-family: var(--vscode-editor-font-family);
            background-color: var(--vscode-editor-background);
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 0.9em;
        }
        .symbol-content {
            font-family: var(--vscode-editor-font-family);
            background-color: var(--vscode-editor-background);
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.85em;
        }
        .no-results {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 40px;
        }
        .loading {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 20px;
        }
        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <h2>Semantic Code Search</h2>
        
        <div class="search-input">
            <input type="text" id="searchQuery" placeholder="Search for functions, classes, variables..." />
            <button onclick="performSearch()">Search</button>
        </div>
        
        <div class="filter-options">
            <div class="filter-option">
                <input type="radio" id="all" name="searchType" value="" checked>
                <label for="all">All</label>
            </div>
            <div class="filter-option">
                <input type="radio" id="function" name="searchType" value="function">
                <label for="function">Functions</label>
            </div>
            <div class="filter-option">
                <input type="radio" id="class" name="searchType" value="class">
                <label for="class">Classes</label>
            </div>
            <div class="filter-option">
                <input type="radio" id="variable" name="searchType" value="variable">
                <label for="variable">Variables</label>
            </div>
            <div class="filter-option">
                <input type="radio" id="interface" name="searchType" value="interface">
                <label for="interface">Interfaces</label>
            </div>
        </div>
    </div>

    <div class="results-container" id="resultsContainer">
        <div class="no-results">
            Enter a search term to find code symbols in your workspace.
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        const searchQuery = document.getElementById('searchQuery');
        const resultsContainer = document.getElementById('resultsContainer');

        searchQuery.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        function performSearch() {
            const query = searchQuery.value.trim();
            if (!query) return;

            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            resultsContainer.innerHTML = '<div class="loading">Searching...</div>';
            
            vscode.postMessage({
                type: 'search',
                query: query,
                type: searchType
            });
        }

        function openFile(filePath, line) {
            vscode.postMessage({
                type: 'openFile',
                filePath: filePath,
                line: line
            });
        }

        window.addEventListener('message', event => {
            const message = event.data;
            
            if (message.type === 'searchResults') {
                displayResults(message.query, message.results);
            } else if (message.type === 'error') {
                displayError(message.message);
            }
        });

        function displayResults(query, results) {
            if (results.length === 0) {
                resultsContainer.innerHTML = \`
                    <div class="no-results">
                        No results found for "\${escapeHtml(query)}".
                    </div>
                \`;
                return;
            }

            let html = \`<h3>Found \${results.length} result\${results.length === 1 ? '' : 's'} for "\${escapeHtml(query)}"</h3>\`;
            
            results.forEach(result => {
                const symbol = result.symbol;
                html += \`
                    <div class="result-item">
                        <div class="result-header">
                            <span class="symbol-name" onclick="openFile('\${symbol.filePath}', \${symbol.startLine})">
                                \${escapeHtml(symbol.name)}
                            </span>
                            <div>
                                <span class="symbol-type">\${symbol.type}</span>
                                <span class="symbol-score">Score: \${result.score.toFixed(1)}</span>
                            </div>
                        </div>
                        
                        <div class="file-path" onclick="openFile('\${symbol.filePath}')">
                            \${symbol.filePath}:\${symbol.startLine}
                        </div>
                        
                        \${symbol.signature ? \`<div class="symbol-signature">\${escapeHtml(symbol.signature)}</div>\` : ''}
                        
                        \${symbol.docstring ? \`<div class="symbol-signature">\${escapeHtml(symbol.docstring)}</div>\` : ''}
                        
                        <div class="symbol-content">\${escapeHtml(symbol.content)}</div>
                    </div>
                \`;
            });
            
            resultsContainer.innerHTML = html;
        }

        function displayError(message) {
            resultsContainer.innerHTML = \`
                <div class="error">
                    Error: \${escapeHtml(message)}
                </div>
            \`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>`;
    }
}
exports.SemanticSearchProvider = SemanticSearchProvider;
//# sourceMappingURL=semanticSearchProvider.js.map