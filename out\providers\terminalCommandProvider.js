"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalCommandProvider = void 0;
const vscode = __importStar(require("vscode"));
const os = __importStar(require("os"));
class TerminalCommandProvider {
    constructor(aiService) {
        this.aiService = aiService;
    }
    async generateCommand() {
        const request = await vscode.window.showInputBox({
            title: 'FastCode Terminal Command Generator',
            prompt: 'Describe what you want to do in the terminal',
            placeHolder: 'e.g., "find all JavaScript files modified in the last week"'
        });
        if (!request?.trim()) {
            return;
        }
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Generating command...',
                cancellable: false
            }, async () => {
                const command = await this.generateShellCommand(request);
                await this.presentCommand(command, request);
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
        }
    }
    async generateShellCommand(request) {
        const platform = os.platform();
        const shell = this.getShellInfo();
        const workspaceInfo = this.getWorkspaceInfo();
        const aiRequest = {
            prompt: this.buildCommandPrompt(request, platform, shell, workspaceInfo),
            maxTokens: 512,
            temperature: 0.1
        };
        const response = await this.aiService.generateCompletion(aiRequest);
        return this.extractCommand(response.content);
    }
    getShellInfo() {
        const terminal = vscode.window.activeTerminal;
        if (terminal) {
            return terminal.name;
        }
        // Default shell detection
        const platform = os.platform();
        switch (platform) {
            case 'win32':
                return 'PowerShell';
            case 'darwin':
            case 'linux':
                return 'bash';
            default:
                return 'shell';
        }
    }
    getWorkspaceInfo() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return 'No workspace folder';
        }
        const rootPath = workspaceFolders[0].uri.fsPath;
        return `Workspace: ${rootPath}`;
    }
    buildCommandPrompt(request, platform, shell, workspaceInfo) {
        return `You are a shell command generator. Generate a command based on the user's request.

Platform: ${platform}
Shell: ${shell}
${workspaceInfo}

User request: ${request}

Requirements:
1. Generate a single, executable command
2. Use appropriate syntax for ${shell} on ${platform}
3. Include necessary flags and options
4. Consider the current workspace context
5. Prioritize safety - avoid destructive operations without confirmation
6. If multiple commands are needed, combine them appropriately (using && or ;)

Provide ONLY the command, without explanations or markdown formatting.

Examples:
- For "find JavaScript files": find . -name "*.js" -type f
- For "list recent commits": git log --oneline -10
- For "install dependencies": npm install

Generate the command:`;
    }
    extractCommand(response) {
        // Remove any markdown formatting
        let command = response.replace(/```[\w]*\n?/g, '').replace(/```/g, '');
        // Remove common prefixes
        command = command.replace(/^(Command:|Shell:|Terminal:)\s*/i, '');
        // Take only the first line if multiple lines
        command = command.split('\n')[0];
        return command.trim();
    }
    async presentCommand(command, originalRequest) {
        const action = await vscode.window.showInformationMessage(`FastCode generated: ${command}`, { modal: false }, 'Run in Terminal', 'Copy to Clipboard', 'Edit Command');
        switch (action) {
            case 'Run in Terminal':
                await this.runInTerminal(command);
                break;
            case 'Copy to Clipboard':
                await vscode.env.clipboard.writeText(command);
                vscode.window.showInformationMessage('Command copied to clipboard');
                break;
            case 'Edit Command':
                await this.editAndRun(command, originalRequest);
                break;
        }
    }
    async runInTerminal(command) {
        let terminal = vscode.window.activeTerminal;
        if (!terminal) {
            terminal = vscode.window.createTerminal('FastCode Terminal');
        }
        terminal.show();
        terminal.sendText(command);
    }
    async editAndRun(command, originalRequest) {
        const editedCommand = await vscode.window.showInputBox({
            title: 'Edit Command',
            value: command,
            prompt: `Original request: ${originalRequest}`
        });
        if (editedCommand?.trim()) {
            await this.runInTerminal(editedCommand);
        }
    }
}
exports.TerminalCommandProvider = TerminalCommandProvider;
//# sourceMappingURL=terminalCommandProvider.js.map