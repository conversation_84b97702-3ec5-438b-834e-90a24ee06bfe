{"version": 3, "file": "terminalCommandProvider.js", "sourceRoot": "", "sources": ["../../src/providers/terminalCommandProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AAGzB,MAAa,uBAAuB;IAChC,YAAoB,SAAyB;QAAzB,cAAS,GAAT,SAAS,CAAgB;IAAG,CAAC;IAEjD,KAAK,CAAC,eAAe;QACjB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,KAAK,EAAE,qCAAqC;YAC5C,MAAM,EAAE,8CAA8C;YACtD,WAAW,EAAE,6DAA6D;SAC7E,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;YAClB,OAAO;SACV;QAED,IAAI;YACA,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,iCAAiC;gBACxC,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,IAAI,EAAE;gBACV,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,KAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAC9C,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9C,MAAM,SAAS,GAAc;YACzB,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC;YACxE,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;SACnB,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAEO,YAAY;QAChB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;QAC9C,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,IAAI,CAAC;SACxB;QAED,0BAA0B;QAC1B,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,QAAQ,QAAQ,EAAE;YACd,KAAK,OAAO;gBACR,OAAO,YAAY,CAAC;YACxB,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACR,OAAO,MAAM,CAAC;YAClB;gBACI,OAAO,OAAO,CAAC;SACtB;IACL,CAAC;IAEO,gBAAgB;QACpB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,OAAO,qBAAqB,CAAC;SAChC;QAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QAChD,OAAO,cAAc,QAAQ,EAAE,CAAC;IACpC,CAAC;IAEO,kBAAkB,CAAC,OAAe,EAAE,QAAgB,EAAE,KAAa,EAAE,aAAqB;QAC9F,OAAO;;YAEH,QAAQ;SACX,KAAK;EACZ,aAAa;;gBAEC,OAAO;;;;gCAIS,KAAK,OAAO,QAAQ;;;;;;;;;;;;;sBAa9B,CAAC;IACnB,CAAC;IAEO,cAAc,CAAC,QAAgB;QACnC,iCAAiC;QACjC,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAEvE,yBAAyB;QACzB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;QAElE,6CAA6C;QAC7C,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjC,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,eAAuB;QACjE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,uBAAuB,OAAO,EAAE,EAChC,EAAE,KAAK,EAAE,KAAK,EAAE,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,CACjB,CAAC;QAEF,QAAQ,MAAM,EAAE;YACZ,KAAK,iBAAiB;gBAClB,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAClC,MAAM;YACV,KAAK,mBAAmB;gBACpB,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC9C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;gBACpE,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;gBAChD,MAAM;SACb;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAe;QACvC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;QAE5C,IAAI,CAAC,QAAQ,EAAE;YACX,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;SAChE;QAED,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChB,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,eAAuB;QAC7D,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACnD,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,qBAAqB,eAAe,EAAE;SACjD,CAAC,CAAC;QAEH,IAAI,aAAa,EAAE,IAAI,EAAE,EAAE;YACvB,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;SAC3C;IACL,CAAC;CACJ;AA7JD,0DA6JC"}