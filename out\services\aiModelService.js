"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModelService = void 0;
const vscode = __importStar(require("vscode"));
const axios_1 = __importDefault(require("axios"));
class AIModelService {
    constructor() {
        this.loadConfig();
        this.updateClient();
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration('fastcode')) {
                this.loadConfig();
                this.updateClient();
            }
        });
    }
    loadConfig() {
        const config = vscode.workspace.getConfiguration('fastcode');
        this.config = {
            endpoint: config.get('apiEndpoint', 'http://localhost:11434'),
            modelName: config.get('modelName', 'codellama:7b'),
            maxTokens: config.get('maxTokens', 2048),
            temperature: config.get('temperature', 0.1),
            timeout: 30000
        };
    }
    updateClient() {
        this.client = axios_1.default.create({
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    async generateCompletion(request) {
        try {
            const response = await this.makeRequest(request);
            return this.parseResponse(response);
        }
        catch (error) {
            throw this.handleError(error);
        }
    }
    async generateStream(request, onChunk) {
        try {
            const response = await this.client.post(`${this.config.endpoint}/api/generate`, {
                model: this.config.modelName,
                prompt: this.buildPrompt(request),
                stream: true,
                options: {
                    temperature: request.temperature ?? this.config.temperature,
                    num_predict: request.maxTokens ?? this.config.maxTokens
                }
            }, {
                responseType: 'stream'
            });
            response.data.on('data', (chunk) => {
                const lines = chunk.toString().split('\n').filter(line => line.trim());
                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        if (data.response) {
                            onChunk(data.response);
                        }
                    }
                    catch (e) {
                        // Ignore parsing errors for incomplete chunks
                    }
                }
            });
            return new Promise((resolve, reject) => {
                response.data.on('end', resolve);
                response.data.on('error', reject);
            });
        }
        catch (error) {
            throw this.handleError(error);
        }
    }
    async makeRequest(request) {
        const payload = this.buildPayload(request);
        // Try different API formats based on the endpoint
        const endpoints = [
            '/api/generate',
            '/v1/completions',
            '/api/v1/generate' // Alternative format
        ];
        let lastError;
        for (const endpoint of endpoints) {
            try {
                const response = await this.client.post(`${this.config.endpoint}${endpoint}`, payload);
                return response;
            }
            catch (error) {
                lastError = error;
                if (error.response?.status !== 404) {
                    break; // If it's not a 404, don't try other endpoints
                }
            }
        }
        throw lastError;
    }
    buildPayload(request) {
        const prompt = this.buildPrompt(request);
        // Try Ollama format first
        return {
            model: this.config.modelName,
            prompt: prompt,
            stream: false,
            options: {
                temperature: request.temperature ?? this.config.temperature,
                num_predict: request.maxTokens ?? this.config.maxTokens
            }
        };
    }
    buildPrompt(request) {
        let prompt = '';
        if (request.context) {
            prompt += `Context:\n${request.context}\n\n`;
        }
        prompt += `Request: ${request.prompt}`;
        return prompt;
    }
    parseResponse(response) {
        const data = response.data;
        // Handle Ollama format
        if (data.response) {
            return {
                content: data.response,
                usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens || 0,
                    completionTokens: data.usage.completion_tokens || 0,
                    totalTokens: data.usage.total_tokens || 0
                } : undefined
            };
        }
        // Handle OpenAI-compatible format
        if (data.choices && data.choices.length > 0) {
            return {
                content: data.choices[0].text || data.choices[0].message?.content || '',
                usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens || 0,
                    completionTokens: data.usage.completion_tokens || 0,
                    totalTokens: data.usage.total_tokens || 0
                } : undefined
            };
        }
        throw new Error('Unexpected response format from AI model');
    }
    handleError(error) {
        if (error.code === 'ECONNREFUSED') {
            return new Error(`Cannot connect to AI model at ${this.config.endpoint}. Please ensure your local AI service is running.`);
        }
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error || error.response.statusText;
            return new Error(`AI model error (${status}): ${message}`);
        }
        if (error.code === 'ENOTFOUND') {
            return new Error(`Invalid AI model endpoint: ${this.config.endpoint}`);
        }
        return new Error(`AI model service error: ${error.message}`);
    }
    async testConnection() {
        try {
            const response = await this.generateCompletion({
                prompt: 'Hello, are you working?',
                maxTokens: 10
            });
            return response.content.length > 0;
        }
        catch (error) {
            console.error('AI model connection test failed:', error);
            return false;
        }
    }
    getConfig() {
        return { ...this.config };
    }
}
exports.AIModelService = AIModelService;
//# sourceMappingURL=aiModelService.js.map