{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/test/suite/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA6B;AAC7B,uCAAyB;AAEzB,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/B,SAAgB,GAAG;IACf,wBAAwB;IACxB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;QACpB,EAAE,EAAE,KAAK;QACT,KAAK,EAAE,IAAI;KACd,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAEhD,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,IAAI;YACA,qCAAqC;YACrC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;YAE3C,8BAA8B;YAC9B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAEzC,qBAAqB;YACrB,KAAK,CAAC,GAAG,CAAC,CAAC,QAAgB,EAAE,EAAE;gBAC3B,IAAI,QAAQ,GAAG,CAAC,EAAE;oBACd,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,QAAQ,gBAAgB,CAAC,CAAC,CAAC;iBAC7C;qBAAM;oBACH,CAAC,EAAE,CAAC;iBACP;YACL,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,GAAG,EAAE;YACV,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,GAAG,CAAC,CAAC;SACV;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AA9BD,kBA8BC;AAED,SAAS,aAAa,CAAC,GAAW;IAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;IAE3B,IAAI;QACA,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBACpB,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1C;iBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBAClC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACxB;SACJ;KACJ;IAAC,OAAO,KAAK,EAAE;QACZ,gBAAgB;KACnB;IAED,OAAO,KAAK,CAAC;AACjB,CAAC"}