{"name": "fastcode", "displayName": "FastCode - Offline AI Assistant", "description": "An offline AI-powered coding assistant for VSCode with local LLM integration", "version": "0.1.0", "publisher": "fastcode", "repository": {"type": "git", "url": "https://github.com/fastcode/fastcode-vscode.git"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "assistant", "local", "offline", "llm", "code-generation", "chat"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "fastcode.inlineChat", "title": "Inline Chat", "category": "FastCode"}, {"command": "fastcode.composer", "title": "Open Composer", "category": "FastCode"}, {"command": "fastcode.globalChat", "title": "Global Chat", "category": "FastCode"}, {"command": "fastcode.autoDebug", "title": "Auto Debug", "category": "FastCode"}, {"command": "fastcode.generateTests", "title": "Generate Unit Tests", "category": "FastCode"}, {"command": "fastcode.generateDocs", "title": "Generate Documentation", "category": "FastCode"}, {"command": "fastcode.terminalCommand", "title": "Terminal Command Generator", "category": "FastCode"}, {"command": "fastcode.semanticSearch", "title": "Semantic Search", "category": "FastCode"}], "keybindings": [{"command": "fastcode.inlineChat", "key": "ctrl+k", "mac": "cmd+k", "when": "editorTextFocus"}, {"command": "fastcode.composer", "key": "ctrl+i", "mac": "cmd+i"}, {"command": "fastcode.globalChat", "key": "ctrl+l", "mac": "cmd+l"}, {"command": "fastcode.globalChat", "key": "ctrl+enter", "mac": "cmd+enter", "when": "editorTextFocus"}, {"command": "fastcode.autoDebug", "key": "alt+enter", "when": "editorTextFocus"}, {"command": "fastcode.applyDiff", "key": "ctrl+shift+v", "mac": "cmd+shift+v"}, {"command": "fastcode.retryRequest", "key": "ctrl+shift+r", "mac": "cmd+shift+r"}], "menus": {"editor/context": [{"command": "fastcode.generateTests", "group": "fastcode@1", "when": "editorHasSelection"}, {"command": "fastcode.generateDocs", "group": "fastcode@2", "when": "editorHasSelection"}]}, "configuration": {"title": "FastCode", "properties": {"fastcode.apiEndpoint": {"type": "string", "default": "http://localhost:11434", "description": "Local AI model API endpoint (e.g., Ollama)"}, "fastcode.modelName": {"type": "string", "default": "codellama:7b", "description": "Name of the local AI model to use"}, "fastcode.maxTokens": {"type": "number", "default": 2048, "description": "Maximum tokens for AI responses"}, "fastcode.temperature": {"type": "number", "default": 0.1, "description": "Temperature for AI model (0.0 to 1.0)"}, "fastcode.enableContextIndex": {"type": "boolean", "default": true, "description": "Enable real-time code context indexing"}, "fastcode.indexedFileTypes": {"type": "array", "default": ["typescript", "javascript", "python", "java", "cpp", "c", "rust", "go"], "description": "File types to include in context indexing"}}}, "views": {"explorer": [{"id": "fastcode.composer", "name": "FastCode Composer", "when": "fastcode.composerVisible"}]}, "viewsContainers": {"panel": [{"id": "fastcode", "title": "FastCode", "icon": "$(robot)"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/diff": "^7.0.2", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0", "eslint": "^8.28.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "chroma-js": "^2.4.2", "diff": "^5.1.0", "sqlite3": "^5.1.6", "tree-sitter": "^0.20.4", "tree-sitter-cpp": "^0.20.0", "tree-sitter-go": "^0.20.0", "tree-sitter-java": "^0.20.2", "tree-sitter-javascript": "^0.20.1", "tree-sitter-python": "^0.20.4", "tree-sitter-rust": "^0.20.4", "tree-sitter-typescript": "^0.20.3"}}