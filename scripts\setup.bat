@echo off
setlocal enabledelayedexpansion

REM FastCode Setup Script for Windows
REM This script helps you set up FastCode with a local AI model

echo.
echo 🚀 FastCode Setup Script for Windows
echo ====================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js is installed: !NODE_VERSION!
)

REM Check if VSCode is installed
echo [INFO] Checking VSCode installation...
code --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] VSCode CLI not found. Make sure VSCode is installed and 'code' command is available.
) else (
    for /f "tokens=*" %%i in ('code --version ^| findstr /n "^" ^| findstr "^1:"') do (
        set VSCODE_VERSION=%%i
        set VSCODE_VERSION=!VSCODE_VERSION:~2!
    )
    echo [SUCCESS] VSCode is installed: !VSCODE_VERSION!
)

REM Ask about Ollama installation
echo.
set /p INSTALL_OLLAMA="Do you want to install Ollama? (y/N): "
if /i "!INSTALL_OLLAMA!"=="y" (
    echo [INFO] Please install Ollama manually from https://ollama.ai/download
    echo [INFO] After installation, run the following commands in a new terminal:
    echo   ollama serve
    echo   ollama pull codellama:7b
    echo.
    pause
)

REM Build extension
echo [INFO] Building FastCode extension...

if not exist "package.json" (
    echo [ERROR] package.json not found. Are you in the FastCode directory?
    pause
    exit /b 1
)

echo [INFO] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)

echo [INFO] Compiling TypeScript...
call npm run compile
if %errorlevel% neq 0 (
    echo [ERROR] Failed to compile TypeScript
    pause
    exit /b 1
)

echo [INFO] Packaging extension...
call npm run package
if %errorlevel% neq 0 (
    echo [ERROR] Failed to package extension
    pause
    exit /b 1
)

echo [SUCCESS] Extension built successfully

REM Install extension in VSCode
echo.
set /p INSTALL_EXT="Do you want to install the extension in VSCode? (Y/n): "
if /i "!INSTALL_EXT!"=="" set INSTALL_EXT=Y
if /i "!INSTALL_EXT!"=="y" (
    echo [INFO] Installing extension in VSCode...
    
    REM Find the VSIX file
    for %%f in (*.vsix) do set VSIX_FILE=%%f
    
    if "!VSIX_FILE!"=="" (
        echo [ERROR] No .vsix file found. Build may have failed.
    ) else (
        code --install-extension "!VSIX_FILE!"
        if %errorlevel% equ 0 (
            echo [SUCCESS] Extension installed in VSCode
        ) else (
            echo [WARNING] Failed to install extension automatically
            echo [WARNING] Please install manually:
            echo [WARNING] 1. Open VSCode
            echo [WARNING] 2. Press Ctrl+Shift+P
            echo [WARNING] 3. Type 'Extensions: Install from VSIX...'
            echo [WARNING] 4. Select: !VSIX_FILE!
        )
    )
)

REM Create configuration
echo [INFO] Creating FastCode configuration...
(
echo {
echo     "fastcode.apiEndpoint": "http://localhost:11434",
echo     "fastcode.modelName": "codellama:7b",
echo     "fastcode.maxTokens": 2048,
echo     "fastcode.temperature": 0.1,
echo     "fastcode.enableContextIndex": true
echo }
) > fastcode-config.json

echo [SUCCESS] Configuration saved to fastcode-config.json

REM Test installation
echo.
echo [INFO] Testing installation...

if exist "out\extension.js" (
    echo [SUCCESS] ✓ Extension compiled successfully
) else (
    echo [ERROR] ✗ Extension compilation failed
)

dir *.vsix >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] ✓ Extension package created
) else (
    echo [ERROR] ✗ Extension package not found
)

REM Final instructions
echo.
echo [SUCCESS] 🎉 FastCode setup completed!
echo.
echo [INFO] Next steps:
echo [INFO] 1. If you haven't already, install Ollama from https://ollama.ai/download
echo [INFO] 2. Start Ollama: ollama serve
echo [INFO] 3. Pull a model: ollama pull codellama:7b
echo [INFO] 4. Open VSCode
echo [INFO] 5. Apply the configuration from fastcode-config.json
echo [INFO] 6. Open a code file and try Ctrl+K for inline chat
echo [INFO] 7. Check the documentation in docs\ for more features
echo.
echo [INFO] Happy coding with FastCode! 🚀
echo.

pause
