#!/bin/bash

# FastCode Setup Script
# This script helps you set up FastCode with a local AI model

set -e

echo "🚀 FastCode Setup Script"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    case "$(uname -s)" in
        Linux*)     OS=Linux;;
        Darwin*)    OS=Mac;;
        CYGWIN*)    OS=Cygwin;;
        MINGW*)     OS=MinGw;;
        *)          OS="UNKNOWN:$(uname -s)"
    esac
    
    print_success "Detected OS: $OS"
}

# Check if Node.js is installed
check_node() {
    print_status "Checking Node.js installation..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
        
        # Check if version is >= 16
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR" -lt 16 ]; then
            print_warning "Node.js version should be 16 or higher. Current: $NODE_VERSION"
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
}

# Check if VSCode is installed
check_vscode() {
    print_status "Checking VSCode installation..."
    
    if command -v code &> /dev/null; then
        VSCODE_VERSION=$(code --version | head -n1)
        print_success "VSCode is installed: $VSCODE_VERSION"
    else
        print_warning "VSCode CLI not found. Make sure VSCode is installed and 'code' command is available."
    fi
}

# Install Ollama
install_ollama() {
    print_status "Installing Ollama..."
    
    if command -v ollama &> /dev/null; then
        print_success "Ollama is already installed"
        return
    fi
    
    case $OS in
        Linux)
            curl -fsSL https://ollama.ai/install.sh | sh
            ;;
        Mac)
            if command -v brew &> /dev/null; then
                brew install ollama
            else
                print_warning "Homebrew not found. Please install Ollama manually from https://ollama.ai/"
                return
            fi
            ;;
        *)
            print_warning "Automatic Ollama installation not supported on $OS"
            print_warning "Please install Ollama manually from https://ollama.ai/"
            return
            ;;
    esac
    
    print_success "Ollama installed successfully"
}

# Start Ollama service
start_ollama() {
    print_status "Starting Ollama service..."
    
    if ! command -v ollama &> /dev/null; then
        print_error "Ollama is not installed"
        return 1
    fi
    
    # Start Ollama in background
    if pgrep -f "ollama serve" > /dev/null; then
        print_success "Ollama is already running"
    else
        print_status "Starting Ollama service..."
        ollama serve &
        sleep 3
        print_success "Ollama service started"
    fi
}

# Pull AI model
pull_model() {
    print_status "Pulling AI model..."
    
    echo "Available models:"
    echo "1. codellama:7b (Recommended, ~4GB)"
    echo "2. codellama:13b (Better quality, ~8GB)"
    echo "3. wizardcoder:7b (Alternative, ~4GB)"
    echo "4. deepseek-coder:6.7b (Excellent for code, ~4GB)"
    
    read -p "Choose a model (1-4) [1]: " model_choice
    model_choice=${model_choice:-1}
    
    case $model_choice in
        1) MODEL="codellama:7b";;
        2) MODEL="codellama:13b";;
        3) MODEL="wizardcoder:7b";;
        4) MODEL="deepseek-coder:6.7b";;
        *) MODEL="codellama:7b";;
    esac
    
    print_status "Pulling model: $MODEL"
    print_warning "This may take several minutes depending on your internet connection..."
    
    if ollama pull "$MODEL"; then
        print_success "Model $MODEL pulled successfully"
        echo "MODEL_NAME=$MODEL" > .env
    else
        print_error "Failed to pull model $MODEL"
        return 1
    fi
}

# Build extension
build_extension() {
    print_status "Building FastCode extension..."
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the FastCode directory?"
        exit 1
    fi
    
    print_status "Installing dependencies..."
    npm install
    
    print_status "Compiling TypeScript..."
    npm run compile
    
    print_status "Packaging extension..."
    npm run package
    
    print_success "Extension built successfully"
}

# Install extension in VSCode
install_extension() {
    print_status "Installing extension in VSCode..."
    
    VSIX_FILE=$(find . -name "*.vsix" | head -n1)
    
    if [ -z "$VSIX_FILE" ]; then
        print_error "No .vsix file found. Build may have failed."
        return 1
    fi
    
    if command -v code &> /dev/null; then
        code --install-extension "$VSIX_FILE"
        print_success "Extension installed in VSCode"
    else
        print_warning "VSCode CLI not available. Please install the extension manually:"
        print_warning "1. Open VSCode"
        print_warning "2. Press Ctrl+Shift+P"
        print_warning "3. Type 'Extensions: Install from VSIX...'"
        print_warning "4. Select: $VSIX_FILE"
    fi
}

# Configure FastCode
configure_fastcode() {
    print_status "Configuring FastCode..."
    
    if [ -f ".env" ]; then
        source .env
    fi
    
    MODEL_NAME=${MODEL_NAME:-"codellama:7b"}
    
    cat > fastcode-config.json << EOF
{
    "fastcode.apiEndpoint": "http://localhost:11434",
    "fastcode.modelName": "$MODEL_NAME",
    "fastcode.maxTokens": 2048,
    "fastcode.temperature": 0.1,
    "fastcode.enableContextIndex": true
}
EOF
    
    print_success "Configuration saved to fastcode-config.json"
    print_status "To apply this configuration:"
    print_status "1. Open VSCode"
    print_status "2. Go to Settings (Ctrl+,)"
    print_status "3. Search for 'FastCode'"
    print_status "4. Apply the settings from fastcode-config.json"
}

# Test installation
test_installation() {
    print_status "Testing installation..."
    
    # Test Ollama
    if command -v ollama &> /dev/null; then
        if ollama list | grep -q "codellama\|wizardcoder\|deepseek"; then
            print_success "✓ Ollama and model are ready"
        else
            print_warning "⚠ Ollama is installed but no models found"
        fi
    else
        print_error "✗ Ollama is not installed"
    fi
    
    # Test extension build
    if [ -f "out/extension.js" ]; then
        print_success "✓ Extension compiled successfully"
    else
        print_error "✗ Extension compilation failed"
    fi
    
    # Test VSIX package
    if ls *.vsix 1> /dev/null 2>&1; then
        print_success "✓ Extension package created"
    else
        print_error "✗ Extension package not found"
    fi
}

# Main setup function
main() {
    echo
    print_status "Starting FastCode setup..."
    echo
    
    check_os
    check_node
    check_vscode
    
    echo
    read -p "Do you want to install Ollama? (y/N): " install_ollama_choice
    if [[ $install_ollama_choice =~ ^[Yy]$ ]]; then
        install_ollama
        start_ollama
        pull_model
    fi
    
    echo
    build_extension
    
    echo
    read -p "Do you want to install the extension in VSCode? (Y/n): " install_ext_choice
    install_ext_choice=${install_ext_choice:-Y}
    if [[ $install_ext_choice =~ ^[Yy]$ ]]; then
        install_extension
    fi
    
    echo
    configure_fastcode
    
    echo
    test_installation
    
    echo
    print_success "🎉 FastCode setup completed!"
    echo
    print_status "Next steps:"
    print_status "1. Open VSCode"
    print_status "2. Apply the configuration from fastcode-config.json"
    print_status "3. Open a code file and try Ctrl+K for inline chat"
    print_status "4. Check the documentation in docs/ for more features"
    echo
    print_status "Happy coding with FastCode! 🚀"
}

# Run main function
main "$@"
