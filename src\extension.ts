import * as vscode from 'vscode';
import { AIModelService } from './services/aiModelService';
import { ContextIndexService } from './services/contextIndexService';
import { InlineChatProvider } from './providers/inlineChatProvider';
import { ComposerProvider } from './providers/composerProvider';
import { GlobalChatProvider } from './providers/globalChatProvider';
import { AutoDebugProvider } from './providers/autoDebugProvider';
import { TestGeneratorProvider } from './providers/testGeneratorProvider';
import { DocGeneratorProvider } from './providers/docGeneratorProvider';
import { TerminalCommandProvider } from './providers/terminalCommandProvider';
import { SemanticSearchProvider } from './providers/semanticSearchProvider';

let aiModelService: AIModelService;
let contextIndexService: ContextIndexService;
let inlineChatProvider: InlineChatProvider;
let composerProvider: ComposerProvider;
let globalChatProvider: GlobalChatProvider;
let autoDebugProvider: AutoDebugProvider;
let testGeneratorProvider: TestGeneratorProvider;
let docGeneratorProvider: DocGeneratorProvider;
let terminalCommandProvider: TerminalCommandProvider;
let semanticSearchProvider: SemanticSearchProvider;

export async function activate(context: vscode.ExtensionContext) {
    console.log('FastCode extension is now active!');

    // Initialize core services
    aiModelService = new AIModelService();
    contextIndexService = new ContextIndexService(context);

    // Initialize providers
    inlineChatProvider = new InlineChatProvider(aiModelService, contextIndexService);
    composerProvider = new ComposerProvider(context.extensionUri, aiModelService, contextIndexService);
    globalChatProvider = new GlobalChatProvider(aiModelService, contextIndexService);
    autoDebugProvider = new AutoDebugProvider(aiModelService, contextIndexService);
    testGeneratorProvider = new TestGeneratorProvider(aiModelService, contextIndexService);
    docGeneratorProvider = new DocGeneratorProvider(aiModelService, contextIndexService);
    terminalCommandProvider = new TerminalCommandProvider(aiModelService);
    semanticSearchProvider = new SemanticSearchProvider(contextIndexService);

    // Register commands
    const commands = [
        vscode.commands.registerCommand('fastcode.inlineChat', () => inlineChatProvider.showInlineChat()),
        vscode.commands.registerCommand('fastcode.composer', () => composerProvider.showComposer()),
        vscode.commands.registerCommand('fastcode.globalChat', () => globalChatProvider.showGlobalChat()),
        vscode.commands.registerCommand('fastcode.autoDebug', () => autoDebugProvider.autoDebug()),
        vscode.commands.registerCommand('fastcode.generateTests', () => testGeneratorProvider.generateTests()),
        vscode.commands.registerCommand('fastcode.generateDocs', () => docGeneratorProvider.generateDocs()),
        vscode.commands.registerCommand('fastcode.terminalCommand', () => terminalCommandProvider.generateCommand()),
        vscode.commands.registerCommand('fastcode.semanticSearch', () => semanticSearchProvider.search()),
        vscode.commands.registerCommand('fastcode.applyDiff', () => inlineChatProvider.applyDiff()),
        vscode.commands.registerCommand('fastcode.retryRequest', () => inlineChatProvider.retryRequest())
    ];

    // Register providers
    context.subscriptions.push(
        ...commands,
        vscode.window.registerWebviewViewProvider('fastcode.composer', composerProvider),
        vscode.languages.registerHoverProvider('*', autoDebugProvider)
    );

    // Start context indexing
    await contextIndexService.initialize();

    // Show welcome message
    vscode.window.showInformationMessage('FastCode is ready! Use Ctrl+K for inline chat, Ctrl+I for composer, or Ctrl+L for global chat.');
}

export function deactivate() {
    if (contextIndexService) {
        contextIndexService.dispose();
    }
}
