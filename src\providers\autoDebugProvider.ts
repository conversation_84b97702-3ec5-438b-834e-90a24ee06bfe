import * as vscode from 'vscode';
import { AIModelService, AIRequest } from '../services/aiModelService';
import { ContextIndexService } from '../services/contextIndexService';

interface ErrorInfo {
    message: string;
    line: number;
    column: number;
    severity: vscode.DiagnosticSeverity;
    source: string;
    code?: string | number;
}

interface FixSuggestion {
    description: string;
    changes: vscode.TextEdit[];
    confidence: number;
}

export class AutoDebugProvider implements vscode.HoverProvider {
    constructor(
        private aiService: AIModelService,
        private contextService: ContextIndexService
    ) {}

    async autoDebug(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const position = editor.selection.active;
        const diagnostics = vscode.languages.getDiagnostics(editor.document.uri);
        
        // Find diagnostics at current line
        const lineErrors = diagnostics.filter(diagnostic => 
            diagnostic.range.start.line === position.line
        );

        if (lineErrors.length === 0) {
            vscode.window.showInformationMessage('No errors found at current line');
            return;
        }

        // Process the most severe error first
        const primaryError = lineErrors.reduce((prev, current) => 
            current.severity < prev.severity ? current : prev
        );

        await this.suggestFix(editor, primaryError);
    }

    private async suggestFix(editor: vscode.TextEditor, diagnostic: vscode.Diagnostic): Promise<void> {
        const errorInfo: ErrorInfo = {
            message: diagnostic.message,
            line: diagnostic.range.start.line + 1,
            column: diagnostic.range.start.character,
            severity: diagnostic.severity,
            source: diagnostic.source || 'unknown',
            code: typeof diagnostic.code === 'object' ? diagnostic.code.value : diagnostic.code
        };

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Analyzing error...',
                cancellable: false
            }, async () => {
                const suggestions = await this.generateFixSuggestions(editor, errorInfo);
                
                if (suggestions.length > 0) {
                    await this.presentFixSuggestions(editor, suggestions);
                } else {
                    vscode.window.showInformationMessage('No automatic fixes available for this error');
                }
            });
        } catch (error: any) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
        }
    }

    private async generateFixSuggestions(editor: vscode.TextEditor, errorInfo: ErrorInfo): Promise<FixSuggestion[]> {
        const document = editor.document;
        const errorLine = document.lineAt(errorInfo.line - 1);
        
        // Get context around the error
        const contextRange = new vscode.Range(
            Math.max(0, errorInfo.line - 11),
            0,
            Math.min(document.lineCount - 1, errorInfo.line + 9),
            0
        );
        const contextCode = document.getText(contextRange);

        // Get semantic context
        const semanticContext = await this.contextService.getContextForPosition(
            document.fileName,
            errorInfo.line,
            errorInfo.column
        );

        // Build AI request
        const aiRequest: AIRequest = {
            prompt: this.buildDebugPrompt(errorInfo, errorLine.text, contextCode, semanticContext),
            maxTokens: 1024,
            temperature: 0.1
        };

        const response = await this.aiService.generateCompletion(aiRequest);
        return this.parseFixSuggestions(response.content, errorInfo, document);
    }

    private buildDebugPrompt(errorInfo: ErrorInfo, errorLine: string, context: string, semanticContext: string): string {
        return `You are a code debugging assistant. Analyze the following error and suggest fixes.

Error Information:
- Message: ${errorInfo.message}
- Line: ${errorInfo.line}
- Column: ${errorInfo.column}
- Source: ${errorInfo.source}
- Code: ${errorInfo.code || 'N/A'}

Error Line:
\`\`\`
${errorLine}
\`\`\`

Context Code:
\`\`\`
${context}
\`\`\`

${semanticContext ? `Semantic Context:\n${semanticContext}\n` : ''}

Please provide 1-3 specific fix suggestions. For each suggestion, provide:
1. A clear description of what the fix does
2. The exact code changes needed
3. A confidence level (1-10)

Format your response as JSON:
{
  "fixes": [
    {
      "description": "Description of the fix",
      "newCode": "The corrected line of code",
      "confidence": 8
    }
  ]
}

Focus on the most likely and practical solutions. Ensure the suggested code is syntactically correct.`;
    }

    private parseFixSuggestions(response: string, errorInfo: ErrorInfo, document: vscode.TextDocument): FixSuggestion[] {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                return [];
            }

            const parsed = JSON.parse(jsonMatch[0]);
            if (!parsed.fixes || !Array.isArray(parsed.fixes)) {
                return [];
            }

            return parsed.fixes.map((fix: any) => {
                const errorRange = new vscode.Range(
                    errorInfo.line - 1, 0,
                    errorInfo.line - 1, document.lineAt(errorInfo.line - 1).text.length
                );

                return {
                    description: fix.description || 'Auto-generated fix',
                    changes: [vscode.TextEdit.replace(errorRange, fix.newCode || '')],
                    confidence: Math.min(10, Math.max(1, fix.confidence || 5))
                };
            }).filter((suggestion: FixSuggestion) => 
                suggestion.changes.length > 0 && suggestion.changes[0].newText.trim()
            );

        } catch (error) {
            console.error('Failed to parse fix suggestions:', error);
            return [];
        }
    }

    private async presentFixSuggestions(editor: vscode.TextEditor, suggestions: FixSuggestion[]): Promise<void> {
        if (suggestions.length === 1) {
            // Single suggestion - show directly
            const suggestion = suggestions[0];
            const action = await vscode.window.showInformationMessage(
                `FastCode suggests: ${suggestion.description} (Confidence: ${suggestion.confidence}/10)`,
                'Apply Fix',
                'Reject'
            );

            if (action === 'Apply Fix') {
                await this.applyFix(editor, suggestion);
            }
        } else {
            // Multiple suggestions - show quick pick
            const items = suggestions.map(suggestion => ({
                label: suggestion.description,
                detail: `Confidence: ${suggestion.confidence}/10`,
                suggestion
            }));

            const selected = await vscode.window.showQuickPick(items, {
                title: 'FastCode: Select a fix',
                placeHolder: 'Choose the best fix for this error'
            });

            if (selected) {
                await this.applyFix(editor, selected.suggestion);
            }
        }
    }

    private async applyFix(editor: vscode.TextEditor, suggestion: FixSuggestion): Promise<void> {
        try {
            const success = await editor.edit(editBuilder => {
                for (const change of suggestion.changes) {
                    editBuilder.replace(change.range, change.newText);
                }
            });

            if (success) {
                vscode.window.showInformationMessage('FastCode: Fix applied successfully');
            } else {
                vscode.window.showErrorMessage('Failed to apply fix');
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to apply fix: ${error.message}`);
        }
    }

    // Hover provider implementation
    async provideHover(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken
    ): Promise<vscode.Hover | null> {
        const diagnostics = vscode.languages.getDiagnostics(document.uri);
        const lineErrors = diagnostics.filter(diagnostic => 
            diagnostic.range.contains(position)
        );

        if (lineErrors.length === 0) {
            return null;
        }

        const errorMessages = lineErrors.map(diagnostic => {
            let message = `**${diagnostic.source || 'Error'}**: ${diagnostic.message}`;
            
            if (diagnostic.code) {
                message += ` (${diagnostic.code})`;
            }

            return message;
        });

        const hoverContent = new vscode.MarkdownString();
        hoverContent.appendMarkdown(errorMessages.join('\n\n'));
        hoverContent.appendMarkdown('\n\n---\n\n');
        hoverContent.appendMarkdown('💡 Press `Alt+Enter` to get AI-powered fix suggestions');

        return new vscode.Hover(hoverContent);
    }
}
