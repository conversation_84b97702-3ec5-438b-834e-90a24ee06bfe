import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AIModelService, AIRequest } from '../services/aiModelService';
import { ContextIndexService } from '../services/contextIndexService';

interface GeneratedFile {
    path: string;
    content: string;
    description: string;
}

interface ComposerSession {
    request: string;
    files: GeneratedFile[];
    status: 'idle' | 'generating' | 'reviewing' | 'complete';
}

export class ComposerProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'fastcode.composer';
    private _view?: vscode.WebviewView;
    private currentSession: ComposerSession | null = null;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private aiService: AIModelService,
        private contextService: ContextIndexService
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'generate':
                    await this.generateFiles(data.request);
                    break;
                case 'apply':
                    await this.applyFiles(data.files);
                    break;
                case 'reject':
                    this.rejectFiles();
                    break;
                case 'retry':
                    if (this.currentSession) {
                        await this.generateFiles(this.currentSession.request);
                    }
                    break;
            }
        });
    }

    public async showComposer() {
        if (this._view) {
            this._view.show?.(true);
        } else {
            // If view is not available, show input box as fallback
            const request = await vscode.window.showInputBox({
                title: 'FastCode Composer',
                prompt: 'Describe the files you want to generate',
                placeHolder: 'e.g., "Create a REST API with Express, TypeScript, and Prisma"'
            });

            if (request) {
                await this.generateFiles(request);
            }
        }
    }

    private async generateFiles(request: string): Promise<void> {
        if (!request.trim()) {
            return;
        }

        this.currentSession = {
            request,
            files: [],
            status: 'generating'
        };

        this.updateWebview();

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Generating files...',
                cancellable: false
            }, async () => {
                const files = await this.generateFilesWithAI(request);
                
                if (this.currentSession) {
                    this.currentSession.files = files;
                    this.currentSession.status = 'reviewing';
                    this.updateWebview();
                }
            });
        } catch (error: any) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
            if (this.currentSession) {
                this.currentSession.status = 'idle';
                this.updateWebview();
            }
        }
    }

    private async generateFilesWithAI(request: string): Promise<GeneratedFile[]> {
        // Get workspace context
        const workspaceContext = await this.getWorkspaceContext();
        
        const aiRequest: AIRequest = {
            prompt: this.buildComposerPrompt(request, workspaceContext),
            maxTokens: 4096,
            temperature: 0.2
        };

        const response = await this.aiService.generateCompletion(aiRequest);
        return this.parseFilesFromResponse(response.content);
    }

    private async getWorkspaceContext(): Promise<string> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return 'No workspace folder detected.';
        }

        const rootPath = workspaceFolders[0].uri.fsPath;
        let context = `Workspace: ${rootPath}\n\n`;

        // Check for common config files
        const configFiles = [
            'package.json',
            'tsconfig.json',
            'requirements.txt',
            'Cargo.toml',
            'go.mod',
            'pom.xml',
            'build.gradle'
        ];

        for (const configFile of configFiles) {
            const configPath = path.join(rootPath, configFile);
            if (fs.existsSync(configPath)) {
                try {
                    const content = fs.readFileSync(configPath, 'utf8');
                    context += `${configFile}:\n\`\`\`json\n${content.substring(0, 1000)}\n\`\`\`\n\n`;
                } catch (error) {
                    // Ignore read errors
                }
            }
        }

        // Get directory structure
        try {
            const structure = await this.getDirectoryStructure(rootPath, 2);
            context += `Directory structure:\n${structure}\n\n`;
        } catch (error) {
            // Ignore errors
        }

        return context;
    }

    private async getDirectoryStructure(dirPath: string, maxDepth: number, currentDepth = 0): Promise<string> {
        if (currentDepth >= maxDepth) {
            return '';
        }

        let structure = '';
        const indent = '  '.repeat(currentDepth);

        try {
            const items = fs.readdirSync(dirPath);
            
            for (const item of items) {
                if (item.startsWith('.') || item === 'node_modules') {
                    continue;
                }

                const itemPath = path.join(dirPath, item);
                const stat = fs.statSync(itemPath);

                if (stat.isDirectory()) {
                    structure += `${indent}${item}/\n`;
                    structure += await this.getDirectoryStructure(itemPath, maxDepth, currentDepth + 1);
                } else {
                    structure += `${indent}${item}\n`;
                }
            }
        } catch (error) {
            // Ignore errors
        }

        return structure;
    }

    private buildComposerPrompt(request: string, workspaceContext: string): string {
        return `You are a code generator that creates multiple files based on user requests. 

${workspaceContext}

User request: ${request}

Please generate the necessary files to fulfill this request. For each file, provide:
1. The file path (relative to workspace root)
2. The complete file content
3. A brief description of what the file does

Format your response as JSON with this structure:
{
  "files": [
    {
      "path": "relative/path/to/file.ext",
      "content": "complete file content here",
      "description": "brief description of the file"
    }
  ]
}

Guidelines:
- Create complete, working files with proper imports and dependencies
- Follow best practices for the target language/framework
- Include necessary configuration files (package.json, tsconfig.json, etc.)
- Add appropriate error handling and validation
- Include basic documentation/comments
- Ensure files work together as a cohesive system

Generate practical, production-ready code that follows modern development practices.`;
    }

    private parseFilesFromResponse(response: string): GeneratedFile[] {
        try {
            // Try to extract JSON from the response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }

            const parsed = JSON.parse(jsonMatch[0]);
            
            if (!parsed.files || !Array.isArray(parsed.files)) {
                throw new Error('Invalid response format');
            }

            return parsed.files.map((file: any) => ({
                path: file.path || '',
                content: file.content || '',
                description: file.description || ''
            })).filter((file: GeneratedFile) => file.path && file.content);

        } catch (error) {
            // Fallback: try to parse files from markdown-style format
            return this.parseFilesFromMarkdown(response);
        }
    }

    private parseFilesFromMarkdown(response: string): GeneratedFile[] {
        const files: GeneratedFile[] = [];
        const fileRegex = /(?:File|Path):\s*([^\n]+)\n(?:Description:\s*([^\n]+)\n)?```[\w]*\n([\s\S]*?)\n```/gi;
        
        let match;
        while ((match = fileRegex.exec(response)) !== null) {
            files.push({
                path: match[1].trim(),
                content: match[3].trim(),
                description: match[2]?.trim() || 'Generated file'
            });
        }

        return files;
    }

    private async applyFiles(files: GeneratedFile[]): Promise<void> {
        if (!files || files.length === 0) {
            return;
        }

        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            vscode.window.showErrorMessage('No workspace folder found');
            return;
        }

        const rootPath = workspaceFolders[0].uri.fsPath;
        let createdFiles = 0;

        try {
            for (const file of files) {
                const fullPath = path.join(rootPath, file.path);
                const dir = path.dirname(fullPath);

                // Create directory if it doesn't exist
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }

                // Write file
                fs.writeFileSync(fullPath, file.content, 'utf8');
                createdFiles++;
            }

            vscode.window.showInformationMessage(`FastCode: Created ${createdFiles} files successfully`);
            
            if (this.currentSession) {
                this.currentSession.status = 'complete';
                this.updateWebview();
            }

            // Open the first file
            if (files.length > 0) {
                const firstFilePath = path.join(rootPath, files[0].path);
                const uri = vscode.Uri.file(firstFilePath);
                await vscode.window.showTextDocument(uri);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to create files: ${error.message}`);
        }
    }

    private rejectFiles(): void {
        if (this.currentSession) {
            this.currentSession.status = 'idle';
            this.currentSession.files = [];
            this.updateWebview();
        }
    }

    private updateWebview(): void {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'update',
                session: this.currentSession
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastCode Composer</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 10px;
        }
        .container {
            max-width: 100%;
        }
        .input-section {
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            padding: 8px;
            font-family: inherit;
            resize: vertical;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            margin: 4px;
            cursor: pointer;
            border-radius: 2px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .file-item {
            border: 1px solid var(--vscode-panel-border);
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .file-path {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        .file-description {
            font-style: italic;
            margin: 5px 0;
        }
        .file-content {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: var(--vscode-editor-font-family);
            font-size: var(--vscode-editor-font-size);
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .status.generating {
            background-color: var(--vscode-inputValidation-warningBackground);
            color: var(--vscode-inputValidation-warningForeground);
        }
        .status.reviewing {
            background-color: var(--vscode-inputValidation-infoBackground);
            color: var(--vscode-inputValidation-infoForeground);
        }
        .status.complete {
            background-color: var(--vscode-inputValidation-successBackground);
            color: var(--vscode-inputValidation-successForeground);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>FastCode Composer</h2>
        
        <div class="input-section">
            <textarea id="requestInput" placeholder="Describe the files you want to generate...
Examples:
• Create a REST API with Express and TypeScript
• Build a React component with hooks and TypeScript
• Generate a Python Flask app with SQLAlchemy"></textarea>
            <br>
            <button id="generateBtn">Generate Files</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
        
        <div id="filesSection" style="display: none;">
            <h3>Generated Files</h3>
            <div id="filesList"></div>
            <div>
                <button id="applyBtn">Apply All Files</button>
                <button id="rejectBtn">Reject</button>
                <button id="retryBtn">Retry</button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        let currentSession = null;

        document.getElementById('generateBtn').addEventListener('click', () => {
            const request = document.getElementById('requestInput').value.trim();
            if (request) {
                vscode.postMessage({
                    type: 'generate',
                    request: request
                });
            }
        });

        document.getElementById('applyBtn').addEventListener('click', () => {
            if (currentSession && currentSession.files) {
                vscode.postMessage({
                    type: 'apply',
                    files: currentSession.files
                });
            }
        });

        document.getElementById('rejectBtn').addEventListener('click', () => {
            vscode.postMessage({ type: 'reject' });
        });

        document.getElementById('retryBtn').addEventListener('click', () => {
            vscode.postMessage({ type: 'retry' });
        });

        window.addEventListener('message', event => {
            const message = event.data;
            
            if (message.type === 'update') {
                currentSession = message.session;
                updateUI();
            }
        });

        function updateUI() {
            const statusEl = document.getElementById('status');
            const filesSectionEl = document.getElementById('filesSection');
            const filesListEl = document.getElementById('filesList');
            
            if (!currentSession) {
                statusEl.style.display = 'none';
                filesSectionEl.style.display = 'none';
                return;
            }

            // Update status
            statusEl.style.display = 'block';
            statusEl.className = 'status ' + currentSession.status;
            
            switch (currentSession.status) {
                case 'generating':
                    statusEl.textContent = 'Generating files...';
                    break;
                case 'reviewing':
                    statusEl.textContent = 'Files generated. Review and apply changes.';
                    break;
                case 'complete':
                    statusEl.textContent = 'Files created successfully!';
                    break;
                default:
                    statusEl.style.display = 'none';
            }

            // Update files list
            if (currentSession.files && currentSession.files.length > 0) {
                filesSectionEl.style.display = 'block';
                filesListEl.innerHTML = '';
                
                currentSession.files.forEach(file => {
                    const fileEl = document.createElement('div');
                    fileEl.className = 'file-item';
                    fileEl.innerHTML = \`
                        <div class="file-path">\${file.path}</div>
                        <div class="file-description">\${file.description}</div>
                        <div class="file-content">\${file.content}</div>
                    \`;
                    filesListEl.appendChild(fileEl);
                });
            } else {
                filesSectionEl.style.display = 'none';
            }
        }
    </script>
</body>
</html>`;
    }
}
