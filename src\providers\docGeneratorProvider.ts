import * as vscode from 'vscode';
import * as path from 'path';
import { AIModelService, AIRequest } from '../services/aiModelService';
import { ContextIndexService } from '../services/contextIndexService';

interface DocFormat {
    name: string;
    extension: string;
    template: string;
}

export class DocGeneratorProvider {
    private docFormats: Map<string, DocFormat[]> = new Map();

    constructor(
        private aiService: AIModelService,
        private contextService: ContextIndexService
    ) {
        this.initializeDocFormats();
    }

    private initializeDocFormats(): void {
        // JavaScript/TypeScript documentation
        this.docFormats.set('javascript', [
            {
                name: 'JSDoc',
                extension: '.js',
                template: 'jsdoc'
            }
        ]);

        this.docFormats.set('typescript', [
            {
                name: 'TSDoc',
                extension: '.ts',
                template: 'tsdoc'
            }
        ]);

        // Python documentation
        this.docFormats.set('python', [
            {
                name: 'Google Style',
                extension: '.py',
                template: 'google'
            },
            {
                name: 'NumPy Style',
                extension: '.py',
                template: 'numpy'
            },
            {
                name: 'Sphinx Style',
                extension: '.py',
                template: 'sphinx'
            }
        ]);

        // Java documentation
        this.docFormats.set('java', [
            {
                name: 'JavaDoc',
                extension: '.java',
                template: 'javadoc'
            }
        ]);

        // Go documentation
        this.docFormats.set('go', [
            {
                name: 'Go Doc',
                extension: '.go',
                template: 'godoc'
            }
        ]);

        // Rust documentation
        this.docFormats.set('rust', [
            {
                name: 'Rust Doc',
                extension: '.rs',
                template: 'rustdoc'
            }
        ]);

        // C++ documentation
        this.docFormats.set('cpp', [
            {
                name: 'Doxygen',
                extension: '.cpp',
                template: 'doxygen'
            }
        ]);
    }

    async generateDocs(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select the code you want to document');
            return;
        }

        const selectedCode = editor.document.getText(selection);
        const language = editor.document.languageId;
        const filePath = editor.document.fileName;

        // Get available documentation formats for this language
        const formats = this.docFormats.get(language);
        if (!formats || formats.length === 0) {
            vscode.window.showErrorMessage(`Documentation generation not supported for ${language}`);
            return;
        }

        // Let user choose format if multiple options
        let selectedFormat: DocFormat;
        if (formats.length === 1) {
            selectedFormat = formats[0];
        } else {
            const formatItems = formats.map(fmt => ({
                label: fmt.name,
                format: fmt
            }));

            const selected = await vscode.window.showQuickPick(formatItems, {
                title: 'Select Documentation Format',
                placeHolder: 'Choose the documentation style to use'
            });

            if (!selected) {
                return;
            }

            selectedFormat = selected.format;
        }

        // Ask user where to place the documentation
        const placement = await vscode.window.showQuickPick([
            { label: 'Above the code', value: 'above' },
            { label: 'Below the code', value: 'below' },
            { label: 'Replace selection', value: 'replace' },
            { label: 'Separate file', value: 'file' }
        ], {
            title: 'Where to place documentation?',
            placeHolder: 'Choose where to insert the generated documentation'
        });

        if (!placement) {
            return;
        }

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Generating documentation...',
                cancellable: false
            }, async () => {
                const documentation = await this.generateDocumentation(
                    selectedCode,
                    language,
                    filePath,
                    selectedFormat
                );

                await this.insertDocumentation(
                    editor,
                    selection,
                    documentation,
                    placement.value,
                    selectedFormat
                );
            });
        } catch (error: any) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
        }
    }

    private async generateDocumentation(
        selectedCode: string,
        language: string,
        filePath: string,
        format: DocFormat
    ): Promise<string> {
        // Get context around the selected code
        const context = await this.contextService.getContextForPosition(
            filePath,
            0,
            0
        );

        const aiRequest: AIRequest = {
            prompt: this.buildDocPrompt(selectedCode, language, context, format),
            maxTokens: 1024,
            temperature: 0.2
        };

        const response = await this.aiService.generateCompletion(aiRequest);
        return this.extractDocumentation(response.content);
    }

    private buildDocPrompt(
        selectedCode: string,
        language: string,
        context: string,
        format: DocFormat
    ): string {
        const examples = this.getDocumentationExamples(format.template);

        return `You are a documentation generation assistant. Generate comprehensive documentation for the given code.

Language: ${language}
Documentation Format: ${format.name}

Code to document:
\`\`\`${language}
${selectedCode}
\`\`\`

${context ? `Context:\n${context}\n` : ''}

Requirements:
1. Use ${format.name} documentation format
2. Include:
   - Clear description of what the code does
   - Parameter descriptions (if applicable)
   - Return value description (if applicable)
   - Usage examples (if helpful)
   - Any important notes or warnings
3. Be concise but comprehensive
4. Use proper formatting for ${format.name}

${examples ? `Example format:\n${examples}\n` : ''}

Generate only the documentation comments, without the original code.`;
    }

    private getDocumentationExamples(template: string): string {
        const examples: { [key: string]: string } = {
            'jsdoc': `/**
 * Calculates the sum of two numbers.
 * @param {number} a - The first number.
 * @param {number} b - The second number.
 * @returns {number} The sum of a and b.
 * @example
 * const result = add(5, 3); // returns 8
 */`,
            'tsdoc': `/**
 * Calculates the sum of two numbers.
 * @param a - The first number.
 * @param b - The second number.
 * @returns The sum of a and b.
 * @example
 * \`\`\`typescript
 * const result = add(5, 3); // returns 8
 * \`\`\`
 */`,
            'google': `"""Calculates the sum of two numbers.

Args:
    a (int): The first number.
    b (int): The second number.

Returns:
    int: The sum of a and b.

Example:
    >>> add(5, 3)
    8
"""`,
            'numpy': `"""Calculates the sum of two numbers.

Parameters
----------
a : int
    The first number.
b : int
    The second number.

Returns
-------
int
    The sum of a and b.

Examples
--------
>>> add(5, 3)
8
"""`,
            'sphinx': `"""Calculates the sum of two numbers.

:param a: The first number.
:type a: int
:param b: The second number.
:type b: int
:returns: The sum of a and b.
:rtype: int

Example:
    >>> add(5, 3)
    8
"""`,
            'javadoc': `/**
 * Calculates the sum of two numbers.
 * @param a The first number.
 * @param b The second number.
 * @return The sum of a and b.
 * @since 1.0
 */`,
            'godoc': `// Add calculates the sum of two numbers.
// It takes two integers and returns their sum.
//
// Example:
//   result := Add(5, 3) // returns 8`,
            'rustdoc': `/// Calculates the sum of two numbers.
///
/// # Arguments
///
/// * \`a\` - The first number.
/// * \`b\` - The second number.
///
/// # Returns
///
/// The sum of a and b.
///
/// # Examples
///
/// \`\`\`
/// let result = add(5, 3);
/// assert_eq!(result, 8);
/// \`\`\``,
            'doxygen': `/**
 * @brief Calculates the sum of two numbers.
 * @param a The first number.
 * @param b The second number.
 * @return The sum of a and b.
 * 
 * @code
 * int result = add(5, 3); // returns 8
 * @endcode
 */`
        };

        return examples[template] || '';
    }

    private extractDocumentation(response: string): string {
        // Remove markdown code blocks if present
        const codeBlockRegex = /```[\w]*\n?([\s\S]*?)\n?```/;
        const match = response.match(codeBlockRegex);
        
        if (match) {
            return match[1].trim();
        }

        return response.trim();
    }

    private async insertDocumentation(
        editor: vscode.TextEditor,
        selection: vscode.Selection,
        documentation: string,
        placement: string,
        format: DocFormat
    ): Promise<void> {
        switch (placement) {
            case 'above':
                await this.insertAbove(editor, selection, documentation);
                break;
            case 'below':
                await this.insertBelow(editor, selection, documentation);
                break;
            case 'replace':
                await this.replaceSelection(editor, selection, documentation);
                break;
            case 'file':
                await this.createDocFile(editor.document.fileName, documentation, format);
                break;
        }
    }

    private async insertAbove(editor: vscode.TextEditor, selection: vscode.Selection, documentation: string): Promise<void> {
        const insertPosition = new vscode.Position(selection.start.line, 0);
        const indentation = this.getIndentation(editor.document, selection.start.line);
        const indentedDoc = this.indentDocumentation(documentation, indentation);
        
        await editor.edit(editBuilder => {
            editBuilder.insert(insertPosition, indentedDoc + '\n');
        });

        vscode.window.showInformationMessage('Documentation inserted above the code');
    }

    private async insertBelow(editor: vscode.TextEditor, selection: vscode.Selection, documentation: string): Promise<void> {
        const insertPosition = new vscode.Position(selection.end.line + 1, 0);
        const indentation = this.getIndentation(editor.document, selection.start.line);
        const indentedDoc = this.indentDocumentation(documentation, indentation);
        
        await editor.edit(editBuilder => {
            editBuilder.insert(insertPosition, indentedDoc + '\n');
        });

        vscode.window.showInformationMessage('Documentation inserted below the code');
    }

    private async replaceSelection(editor: vscode.TextEditor, selection: vscode.Selection, documentation: string): Promise<void> {
        const indentation = this.getIndentation(editor.document, selection.start.line);
        const indentedDoc = this.indentDocumentation(documentation, indentation);
        
        await editor.edit(editBuilder => {
            editBuilder.replace(selection, indentedDoc);
        });

        vscode.window.showInformationMessage('Selection replaced with documentation');
    }

    private async createDocFile(originalFilePath: string, documentation: string, format: DocFormat): Promise<void> {
        const dir = path.dirname(originalFilePath);
        const baseName = path.basename(originalFilePath, path.extname(originalFilePath));
        const docFileName = `${baseName}_docs.md`;
        const docFilePath = path.join(dir, docFileName);

        const docContent = `# Documentation for ${baseName}\n\n${documentation}`;

        const uri = vscode.Uri.file(docFilePath);
        const doc = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(doc);
        
        await editor.edit(editBuilder => {
            editBuilder.insert(new vscode.Position(0, 0), docContent);
        });

        vscode.window.showInformationMessage(`Documentation file created: ${docFileName}`);
    }

    private getIndentation(document: vscode.TextDocument, lineNumber: number): string {
        const line = document.lineAt(lineNumber);
        const match = line.text.match(/^(\s*)/);
        return match ? match[1] : '';
    }

    private indentDocumentation(documentation: string, indentation: string): string {
        return documentation
            .split('\n')
            .map(line => line ? indentation + line : line)
            .join('\n');
    }
}
