import * as vscode from 'vscode';
import { AIModelService, AIRequest } from '../services/aiModelService';
import { ContextIndexService, SearchResult } from '../services/contextIndexService';

interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    references?: string[];
}

interface ChatSession {
    messages: ChatMessage[];
    isActive: boolean;
}

export class GlobalChatProvider {
    private chatPanel: vscode.WebviewPanel | null = null;
    private currentSession: ChatSession = {
        messages: [],
        isActive: false
    };

    constructor(
        private aiService: AIModelService,
        private contextService: ContextIndexService
    ) {}

    async showGlobalChat(): Promise<void> {
        if (this.chatPanel) {
            this.chatPanel.reveal();
            return;
        }

        this.chatPanel = vscode.window.createWebviewPanel(
            'fastcodeGlobalChat',
            'FastCode Chat',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.chatPanel.webview.html = this.getWebviewContent();

        this.chatPanel.webview.onDidReceiveMessage(async (message) => {
            switch (message.type) {
                case 'sendMessage':
                    await this.handleUserMessage(message.content);
                    break;
                case 'clearChat':
                    this.clearChat();
                    break;
                case 'openFile':
                    await this.openFile(message.filePath, message.line);
                    break;
            }
        });

        this.chatPanel.onDidDispose(() => {
            this.chatPanel = null;
            this.currentSession.isActive = false;
        });

        this.currentSession.isActive = true;
        this.updateWebview();
    }

    private async handleUserMessage(content: string): Promise<void> {
        if (!content.trim()) {
            return;
        }

        // Add user message
        const userMessage: ChatMessage = {
            role: 'user',
            content: content.trim(),
            timestamp: new Date()
        };

        this.currentSession.messages.push(userMessage);
        this.updateWebview();

        try {
            // Search for relevant code context
            const searchResults = await this.searchRelevantContext(content);
            
            // Build context from search results
            const context = this.buildContextFromResults(searchResults);
            
            // Generate AI response
            const aiRequest: AIRequest = {
                prompt: this.buildChatPrompt(content, context),
                maxTokens: 2048,
                temperature: 0.3
            };

            const response = await this.aiService.generateCompletion(aiRequest);
            
            // Extract file references from response
            const references = this.extractFileReferences(response.content, searchResults);

            // Add assistant message
            const assistantMessage: ChatMessage = {
                role: 'assistant',
                content: response.content,
                timestamp: new Date(),
                references
            };

            this.currentSession.messages.push(assistantMessage);
            this.updateWebview();

        } catch (error: any) {
            const errorMessage: ChatMessage = {
                role: 'assistant',
                content: `Sorry, I encountered an error: ${error.message}`,
                timestamp: new Date()
            };

            this.currentSession.messages.push(errorMessage);
            this.updateWebview();
        }
    }

    private async searchRelevantContext(query: string): Promise<SearchResult[]> {
        // Extract potential symbol names from the query
        const symbolMatches = query.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
        
        let allResults: SearchResult[] = [];
        
        // Search for each potential symbol
        for (const symbol of symbolMatches) {
            if (symbol.length > 2) { // Ignore very short words
                const results = await this.contextService.searchSymbols(symbol);
                allResults = allResults.concat(results);
            }
        }

        // Remove duplicates and sort by relevance
        const uniqueResults = allResults.filter((result, index, self) => 
            index === self.findIndex(r => r.symbol.filePath === result.symbol.filePath && 
                                         r.symbol.name === result.symbol.name)
        );

        return uniqueResults.sort((a, b) => b.score - a.score).slice(0, 10);
    }

    private buildContextFromResults(results: SearchResult[]): string {
        if (results.length === 0) {
            return '';
        }

        let context = 'Relevant code context:\n\n';
        
        for (const result of results) {
            const symbol = result.symbol;
            context += `File: ${symbol.filePath}\n`;
            context += `Symbol: ${symbol.name} (${symbol.type})\n`;
            
            if (symbol.signature) {
                context += `Signature: ${symbol.signature}\n`;
            }
            
            if (symbol.docstring) {
                context += `Documentation: ${symbol.docstring}\n`;
            }
            
            context += `Code:\n\`\`\`\n${symbol.content}\n\`\`\`\n\n`;
        }

        return context;
    }

    private buildChatPrompt(userMessage: string, context: string): string {
        const conversationHistory = this.currentSession.messages
            .slice(-6) // Last 6 messages for context
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n\n');

        return `You are FastCode, an AI coding assistant. You help developers understand and work with their codebase.

${context ? `${context}\n` : ''}

Conversation history:
${conversationHistory}

User: ${userMessage}

Please provide a helpful response. If you reference specific files or code symbols, mention them clearly so they can be linked. Be concise but thorough.`;
    }

    private extractFileReferences(content: string, searchResults: SearchResult[]): string[] {
        const references: string[] = [];

        // Extract file paths mentioned in the response
        const filePathRegex = /([a-zA-Z0-9_\-\/\\\.]+\.(ts|js|py|java|cpp|c|rs|go|h|hpp))/g;
        const matches = content.match(filePathRegex);

        if (matches) {
            references.push(...matches);
        }

        // Add files from search results that were likely referenced
        for (const result of searchResults) {
            if (content.includes(result.symbol.name)) {
                references.push(result.symbol.filePath);
            }
        }

        // Remove duplicates
        return [...new Set(references)];
    }

    private clearChat(): void {
        this.currentSession.messages = [];
        this.updateWebview();
    }

    private async openFile(filePath: string, line?: number): Promise<void> {
        try {
            const uri = vscode.Uri.file(filePath);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document);

            if (line && line > 0) {
                const position = new vscode.Position(line - 1, 0);
                editor.selection = new vscode.Selection(position, position);
                editor.revealRange(new vscode.Range(position, position));
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Could not open file: ${filePath}`);
        }
    }

    private updateWebview(): void {
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'updateChat',
                messages: this.currentSession.messages
            });
        }
    }

    private getWebviewContent(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastCode Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .chat-header {
            padding: 10px;
            border-bottom: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-panel-background);
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .message.user {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            margin-left: 20px;
        }
        .message.assistant {
            background-color: var(--vscode-textCodeBlock-background);
            margin-right: 20px;
        }
        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 0.9em;
            opacity: 0.8;
        }
        .message-content {
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .message-references {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid var(--vscode-panel-border);
        }
        .reference-link {
            display: inline-block;
            margin: 2px 5px 2px 0;
            padding: 2px 8px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.8em;
            cursor: pointer;
        }
        .reference-link:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .chat-input {
            border-top: 1px solid var(--vscode-panel-border);
            padding: 10px;
            background-color: var(--vscode-panel-background);
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        #messageInput {
            flex: 1;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            padding: 8px;
            border-radius: 4px;
            font-family: inherit;
            resize: none;
            min-height: 20px;
            max-height: 100px;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .clear-button {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        .clear-button:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h3>FastCode Chat</h3>
            <button class="clear-button" onclick="clearChat()">Clear Chat</button>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-header">FastCode</div>
                <div class="message-content">Hello! I'm FastCode, your AI coding assistant. I can help you understand your codebase, answer questions about your code, and assist with development tasks. What would you like to know?</div>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-container">
                <textarea id="messageInput" placeholder="Ask me about your code..." rows="1"></textarea>
                <button onclick="sendMessage()" id="sendButton">Send</button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatMessages = document.getElementById('chatMessages');

        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        messageInput.addEventListener('input', () => {
            // Auto-resize textarea
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 100) + 'px';
        });

        function sendMessage() {
            const content = messageInput.value.trim();
            if (!content) return;

            vscode.postMessage({
                type: 'sendMessage',
                content: content
            });

            messageInput.value = '';
            messageInput.style.height = 'auto';
        }

        function clearChat() {
            vscode.postMessage({
                type: 'clearChat'
            });
        }

        function openFile(filePath, line) {
            vscode.postMessage({
                type: 'openFile',
                filePath: filePath,
                line: line
            });
        }

        window.addEventListener('message', event => {
            const message = event.data;

            if (message.type === 'updateChat') {
                updateChatMessages(message.messages);
            }
        });

        function updateChatMessages(messages) {
            chatMessages.innerHTML = '';

            // Add welcome message if no messages
            if (messages.length === 0) {
                const welcomeMsg = document.createElement('div');
                welcomeMsg.className = 'message assistant';
                welcomeMsg.innerHTML = \`
                    <div class="message-header">FastCode</div>
                    <div class="message-content">Hello! I'm FastCode, your AI coding assistant. I can help you understand your codebase, answer questions about your code, and assist with development tasks. What would you like to know?</div>
                \`;
                chatMessages.appendChild(welcomeMsg);
                return;
            }

            messages.forEach(msg => {
                const messageEl = document.createElement('div');
                messageEl.className = \`message \${msg.role}\`;

                let content = \`
                    <div class="message-header">\${msg.role === 'user' ? 'You' : 'FastCode'}</div>
                    <div class="message-content">\${escapeHtml(msg.content)}</div>
                \`;

                if (msg.references && msg.references.length > 0) {
                    content += '<div class="message-references">';
                    msg.references.forEach(ref => {
                        content += \`<span class="reference-link" onclick="openFile('\${ref}')">\${ref}</span>\`;
                    });
                    content += '</div>';
                }

                messageEl.innerHTML = content;
                chatMessages.appendChild(messageEl);
            });

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>`;
    }
}
