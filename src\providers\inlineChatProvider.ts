import * as vscode from 'vscode';
import { AIModelService, AIRequest } from '../services/aiModelService';
import { ContextIndexService } from '../services/contextIndexService';
import * as diff from 'diff';

interface InlineChatSession {
    editor: vscode.TextEditor;
    selection: vscode.Selection;
    originalText: string;
    inputBox: vscode.InputBox;
    decorationType: vscode.TextEditorDecorationType;
    lastResponse?: string;
    lastRequest?: string;
}

export class InlineChatProvider {
    private currentSession: InlineChatSession | null = null;
    private pendingDiff: string | null = null;

    constructor(
        private aiService: AIModelService,
        private contextService: ContextIndexService
    ) {}

    async showInlineChat(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        // Clean up any existing session
        this.cleanupSession();

        const selection = editor.selection;
        const originalText = editor.document.getText(selection);

        // Create input box
        const inputBox = vscode.window.createInputBox();
        inputBox.title = 'FastCode Inline Chat';
        inputBox.placeholder = 'Enter your request (e.g., "make this async", "add error handling")';
        inputBox.prompt = 'What would you like me to do with the selected code?';

        // Create decoration for highlighting
        const decorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: new vscode.ThemeColor('editor.findMatchHighlightBackground'),
            border: '1px solid',
            borderColor: new vscode.ThemeColor('editor.findMatchBorder')
        });

        // Highlight the selection
        editor.setDecorations(decorationType, [selection]);

        // Create session
        this.currentSession = {
            editor,
            selection,
            originalText,
            inputBox,
            decorationType,
        };

        // Handle input
        inputBox.onDidAccept(async () => {
            const request = inputBox.value.trim();
            if (!request) {
                return;
            }

            inputBox.hide();
            await this.processInlineRequest(request);
        });

        inputBox.onDidHide(() => {
            this.cleanupSession();
        });

        inputBox.show();
    }

    private async processInlineRequest(request: string): Promise<void> {
        if (!this.currentSession) {
            return;
        }

        const { editor, selection, originalText } = this.currentSession;
        this.currentSession.lastRequest = request;

        try {
            // Show progress
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Processing request...',
                cancellable: false
            }, async () => {
                // Get context
                const context = await this.buildContext(editor, selection);

                // Build AI request
                const aiRequest: AIRequest = {
                    prompt: this.buildInlinePrompt(request, originalText, context),
                    maxTokens: 1024,
                    temperature: 0.1
                };

                // Get AI response
                const response = await this.aiService.generateCompletion(aiRequest);
                const newCode = this.extractCodeFromResponse(response.content);

                if (newCode && newCode !== originalText) {
                    this.currentSession!.lastResponse = newCode;
                    await this.showDiffPreview(originalText, newCode);
                } else {
                    vscode.window.showWarningMessage('No changes suggested by AI');
                    this.cleanupSession();
                }
            });
        } catch (error: any) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
            this.cleanupSession();
        }
    }

    private async buildContext(editor: vscode.TextEditor, selection: vscode.Selection): Promise<string> {
        const filePath = editor.document.fileName;
        const line = selection.start.line + 1;
        const column = selection.start.character;

        // Get surrounding context
        const contextRange = new vscode.Range(
            Math.max(0, selection.start.line - 10),
            0,
            Math.min(editor.document.lineCount - 1, selection.end.line + 10),
            0
        );
        const surroundingCode = editor.document.getText(contextRange);

        // Get semantic context from index
        const semanticContext = await this.contextService.getContextForPosition(filePath, line, column);

        return `File: ${filePath}
Language: ${editor.document.languageId}

Surrounding code:
\`\`\`${editor.document.languageId}
${surroundingCode}
\`\`\`

${semanticContext ? `Semantic context:\n${semanticContext}` : ''}`;
    }

    private buildInlinePrompt(request: string, originalCode: string, context: string): string {
        return `You are a code assistant. The user has selected some code and wants you to modify it.

${context}

Selected code to modify:
\`\`\`
${originalCode}
\`\`\`

User request: ${request}

Please provide the modified code. Return ONLY the modified code without any explanations or markdown formatting. The code should be ready to replace the selected text directly.`;
    }

    private extractCodeFromResponse(response: string): string {
        // Remove markdown code blocks if present
        const codeBlockRegex = /```[\w]*\n?([\s\S]*?)\n?```/;
        const match = response.match(codeBlockRegex);
        
        if (match) {
            return match[1].trim();
        }

        // If no code blocks, return the response as-is (trimmed)
        return response.trim();
    }

    private async showDiffPreview(originalCode: string, newCode: string): Promise<void> {
        if (!this.currentSession) {
            return;
        }

        this.pendingDiff = newCode;

        // Create diff view
        const diffText = this.createDiffText(originalCode, newCode);
        
        // Show diff in a new document
        const diffDoc = await vscode.workspace.openTextDocument({
            content: diffText,
            language: 'diff'
        });

        await vscode.window.showTextDocument(diffDoc, {
            viewColumn: vscode.ViewColumn.Beside,
            preview: true
        });

        // Show action buttons
        const action = await vscode.window.showInformationMessage(
            'FastCode: Review the changes',
            { modal: false },
            'Apply',
            'Reject',
            'Retry'
        );

        switch (action) {
            case 'Apply':
                await this.applyDiff();
                break;
            case 'Reject':
                this.cleanupSession();
                break;
            case 'Retry':
                await this.retryRequest();
                break;
            default:
                this.cleanupSession();
                break;
        }
    }

    private createDiffText(originalCode: string, newCode: string): string {
        const changes = diff.createPatch('code', originalCode, newCode, 'Original', 'Modified');
        return changes;
    }

    async applyDiff(): Promise<void> {
        if (!this.currentSession || !this.pendingDiff) {
            return;
        }

        const { editor, selection } = this.currentSession;

        try {
            await editor.edit(editBuilder => {
                editBuilder.replace(selection, this.pendingDiff!);
            });

            vscode.window.showInformationMessage('FastCode: Changes applied successfully');
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to apply changes: ${error.message}`);
        } finally {
            this.cleanupSession();
        }
    }

    async retryRequest(): Promise<void> {
        if (!this.currentSession || !this.currentSession.lastRequest) {
            return;
        }

        const request = this.currentSession.lastRequest;
        await this.processInlineRequest(request);
    }

    private cleanupSession(): void {
        if (this.currentSession) {
            // Remove decorations
            this.currentSession.editor.setDecorations(this.currentSession.decorationType, []);
            this.currentSession.decorationType.dispose();

            // Hide input box if still visible
            this.currentSession.inputBox.hide();
            this.currentSession.inputBox.dispose();

            this.currentSession = null;
        }

        this.pendingDiff = null;

        // Close diff preview if open
        vscode.commands.executeCommand('workbench.action.closeActiveEditor');
    }
}
