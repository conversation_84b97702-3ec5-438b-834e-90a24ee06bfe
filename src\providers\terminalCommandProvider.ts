import * as vscode from 'vscode';
import * as os from 'os';
import { AIModelService, AIRequest } from '../services/aiModelService';

export class TerminalCommandProvider {
    constructor(private aiService: AIModelService) {}

    async generateCommand(): Promise<void> {
        const request = await vscode.window.showInputBox({
            title: 'FastCode Terminal Command Generator',
            prompt: 'Describe what you want to do in the terminal',
            placeHolder: 'e.g., "find all JavaScript files modified in the last week"'
        });

        if (!request?.trim()) {
            return;
        }

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Generating command...',
                cancellable: false
            }, async () => {
                const command = await this.generateShellCommand(request);
                await this.presentCommand(command, request);
            });
        } catch (error: any) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
        }
    }

    private async generateShellCommand(request: string): Promise<string> {
        const platform = os.platform();
        const shell = this.getShellInfo();
        const workspaceInfo = this.getWorkspaceInfo();

        const aiRequest: AIRequest = {
            prompt: this.buildCommandPrompt(request, platform, shell, workspaceInfo),
            maxTokens: 512,
            temperature: 0.1
        };

        const response = await this.aiService.generateCompletion(aiRequest);
        return this.extractCommand(response.content);
    }

    private getShellInfo(): string {
        const terminal = vscode.window.activeTerminal;
        if (terminal) {
            return terminal.name;
        }

        // Default shell detection
        const platform = os.platform();
        switch (platform) {
            case 'win32':
                return 'PowerShell';
            case 'darwin':
            case 'linux':
                return 'bash';
            default:
                return 'shell';
        }
    }

    private getWorkspaceInfo(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return 'No workspace folder';
        }

        const rootPath = workspaceFolders[0].uri.fsPath;
        return `Workspace: ${rootPath}`;
    }

    private buildCommandPrompt(request: string, platform: string, shell: string, workspaceInfo: string): string {
        return `You are a shell command generator. Generate a command based on the user's request.

Platform: ${platform}
Shell: ${shell}
${workspaceInfo}

User request: ${request}

Requirements:
1. Generate a single, executable command
2. Use appropriate syntax for ${shell} on ${platform}
3. Include necessary flags and options
4. Consider the current workspace context
5. Prioritize safety - avoid destructive operations without confirmation
6. If multiple commands are needed, combine them appropriately (using && or ;)

Provide ONLY the command, without explanations or markdown formatting.

Examples:
- For "find JavaScript files": find . -name "*.js" -type f
- For "list recent commits": git log --oneline -10
- For "install dependencies": npm install

Generate the command:`;
    }

    private extractCommand(response: string): string {
        // Remove any markdown formatting
        let command = response.replace(/```[\w]*\n?/g, '').replace(/```/g, '');
        
        // Remove common prefixes
        command = command.replace(/^(Command:|Shell:|Terminal:)\s*/i, '');
        
        // Take only the first line if multiple lines
        command = command.split('\n')[0];
        
        return command.trim();
    }

    private async presentCommand(command: string, originalRequest: string): Promise<void> {
        const action = await vscode.window.showInformationMessage(
            `FastCode generated: ${command}`,
            { modal: false },
            'Run in Terminal',
            'Copy to Clipboard',
            'Edit Command'
        );

        switch (action) {
            case 'Run in Terminal':
                await this.runInTerminal(command);
                break;
            case 'Copy to Clipboard':
                await vscode.env.clipboard.writeText(command);
                vscode.window.showInformationMessage('Command copied to clipboard');
                break;
            case 'Edit Command':
                await this.editAndRun(command, originalRequest);
                break;
        }
    }

    private async runInTerminal(command: string): Promise<void> {
        let terminal = vscode.window.activeTerminal;
        
        if (!terminal) {
            terminal = vscode.window.createTerminal('FastCode Terminal');
        }

        terminal.show();
        terminal.sendText(command);
    }

    private async editAndRun(command: string, originalRequest: string): Promise<void> {
        const editedCommand = await vscode.window.showInputBox({
            title: 'Edit Command',
            value: command,
            prompt: `Original request: ${originalRequest}`
        });

        if (editedCommand?.trim()) {
            await this.runInTerminal(editedCommand);
        }
    }
}
