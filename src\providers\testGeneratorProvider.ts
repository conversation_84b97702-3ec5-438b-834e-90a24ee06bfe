import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AIModelService, AIRequest } from '../services/aiModelService';
import { ContextIndexService } from '../services/contextIndexService';

interface TestFrameworkInfo {
    name: string;
    fileExtension: string;
    importStatement: string;
    testTemplate: string;
}

export class TestGeneratorProvider {
    private testFrameworks: Map<string, TestFrameworkInfo[]> = new Map();

    constructor(
        private aiService: AIModelService,
        private contextService: ContextIndexService
    ) {
        this.initializeTestFrameworks();
    }

    private initializeTestFrameworks(): void {
        // JavaScript/TypeScript frameworks
        this.testFrameworks.set('javascript', [
            {
                name: 'Jest',
                fileExtension: '.test.js',
                importStatement: "const { test, expect, describe, beforeEach, afterEach } = require('@jest/globals');",
                testTemplate: 'jest'
            },
            {
                name: '<PERSON><PERSON> + <PERSON><PERSON>',
                fileExtension: '.test.js',
                importStatement: "const { describe, it, beforeEach, afterEach } = require('mocha');\nconst { expect } = require('chai');",
                testTemplate: 'mocha'
            }
        ]);

        this.testFrameworks.set('typescript', [
            {
                name: 'Jest',
                fileExtension: '.test.ts',
                importStatement: "import { test, expect, describe, beforeEach, afterEach } from '@jest/globals';",
                testTemplate: 'jest'
            },
            {
                name: 'Vitest',
                fileExtension: '.test.ts',
                importStatement: "import { test, expect, describe, beforeEach, afterEach } from 'vitest';",
                testTemplate: 'vitest'
            }
        ]);

        // Python frameworks
        this.testFrameworks.set('python', [
            {
                name: 'pytest',
                fileExtension: '_test.py',
                importStatement: 'import pytest\nimport unittest.mock as mock',
                testTemplate: 'pytest'
            },
            {
                name: 'unittest',
                fileExtension: '_test.py',
                importStatement: 'import unittest\nimport unittest.mock as mock',
                testTemplate: 'unittest'
            }
        ]);

        // Java frameworks
        this.testFrameworks.set('java', [
            {
                name: 'JUnit 5',
                fileExtension: 'Test.java',
                importStatement: 'import org.junit.jupiter.api.*;\nimport static org.junit.jupiter.api.Assertions.*;',
                testTemplate: 'junit5'
            }
        ]);

        // Go frameworks
        this.testFrameworks.set('go', [
            {
                name: 'Go Testing',
                fileExtension: '_test.go',
                importStatement: 'import (\n\t"testing"\n)',
                testTemplate: 'go'
            }
        ]);

        // Rust frameworks
        this.testFrameworks.set('rust', [
            {
                name: 'Rust Tests',
                fileExtension: '.rs',
                importStatement: '',
                testTemplate: 'rust'
            }
        ]);
    }

    async generateTests(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select the code you want to generate tests for');
            return;
        }

        const selectedCode = editor.document.getText(selection);
        const language = editor.document.languageId;
        const filePath = editor.document.fileName;

        // Get available test frameworks for this language
        const frameworks = this.testFrameworks.get(language);
        if (!frameworks || frameworks.length === 0) {
            vscode.window.showErrorMessage(`Test generation not supported for ${language}`);
            return;
        }

        // Let user choose framework if multiple options
        let selectedFramework: TestFrameworkInfo;
        if (frameworks.length === 1) {
            selectedFramework = frameworks[0];
        } else {
            const frameworkItems = frameworks.map(fw => ({
                label: fw.name,
                framework: fw
            }));

            const selected = await vscode.window.showQuickPick(frameworkItems, {
                title: 'Select Test Framework',
                placeHolder: 'Choose the testing framework to use'
            });

            if (!selected) {
                return;
            }

            selectedFramework = selected.framework;
        }

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'FastCode: Generating tests...',
                cancellable: false
            }, async () => {
                const testCode = await this.generateTestCode(
                    selectedCode,
                    language,
                    filePath,
                    selectedFramework
                );

                await this.createTestFile(filePath, testCode, selectedFramework);
            });
        } catch (error: any) {
            vscode.window.showErrorMessage(`FastCode error: ${error.message}`);
        }
    }

    private async generateTestCode(
        selectedCode: string,
        language: string,
        filePath: string,
        framework: TestFrameworkInfo
    ): Promise<string> {
        // Get context around the selected code
        const context = await this.contextService.getContextForPosition(
            filePath,
            0, // We'll get general context for the file
            0
        );

        const aiRequest: AIRequest = {
            prompt: this.buildTestPrompt(selectedCode, language, context, framework),
            maxTokens: 2048,
            temperature: 0.2
        };

        const response = await this.aiService.generateCompletion(aiRequest);
        return this.extractTestCode(response.content);
    }

    private buildTestPrompt(
        selectedCode: string,
        language: string,
        context: string,
        framework: TestFrameworkInfo
    ): string {
        return `You are a test generation assistant. Generate comprehensive unit tests for the given code.

Language: ${language}
Test Framework: ${framework.name}

Code to test:
\`\`\`${language}
${selectedCode}
\`\`\`

${context ? `Context:\n${context}\n` : ''}

Requirements:
1. Use ${framework.name} testing framework
2. Include the following import statement: ${framework.importStatement}
3. Generate tests for:
   - Happy path scenarios
   - Edge cases
   - Error conditions
   - Boundary values
4. Use descriptive test names
5. Include setup/teardown if needed
6. Mock external dependencies
7. Ensure good test coverage

Generate complete, runnable test code. Include only the test code without explanations.`;
    }

    private extractTestCode(response: string): string {
        // Remove markdown code blocks if present
        const codeBlockRegex = /```[\w]*\n?([\s\S]*?)\n?```/;
        const match = response.match(codeBlockRegex);
        
        if (match) {
            return match[1].trim();
        }

        return response.trim();
    }

    private async createTestFile(
        originalFilePath: string,
        testCode: string,
        framework: TestFrameworkInfo
    ): Promise<void> {
        const dir = path.dirname(originalFilePath);
        const baseName = path.basename(originalFilePath, path.extname(originalFilePath));
        
        let testFileName: string;
        
        // Generate test file name based on framework conventions
        if (framework.fileExtension.startsWith('_')) {
            // Python style: module_test.py
            testFileName = baseName + framework.fileExtension;
        } else if (framework.fileExtension.endsWith('Test.java')) {
            // Java style: ModuleTest.java
            testFileName = baseName + 'Test.java';
        } else {
            // JavaScript/TypeScript style: module.test.js
            testFileName = baseName + framework.fileExtension;
        }

        const testFilePath = path.join(dir, testFileName);

        // Check if test file already exists
        if (fs.existsSync(testFilePath)) {
            const action = await vscode.window.showWarningMessage(
                `Test file ${testFileName} already exists. What would you like to do?`,
                'Overwrite',
                'Append',
                'Cancel'
            );

            if (action === 'Cancel') {
                return;
            }

            if (action === 'Append') {
                const existingContent = fs.readFileSync(testFilePath, 'utf8');
                testCode = existingContent + '\n\n' + testCode;
            }
        }

        // Write test file
        fs.writeFileSync(testFilePath, testCode, 'utf8');

        // Open the test file
        const uri = vscode.Uri.file(testFilePath);
        await vscode.window.showTextDocument(uri);

        vscode.window.showInformationMessage(`Test file created: ${testFileName}`);
    }
}
