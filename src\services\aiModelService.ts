import * as vscode from 'vscode';
import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface AIRequest {
    prompt: string;
    context?: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
}

export interface AIResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export interface ModelConfig {
    endpoint: string;
    modelName: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
}

export class AIModelService {
    private client!: AxiosInstance;
    private config!: ModelConfig;

    constructor() {
        this.loadConfig();
        this.updateClient();

        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration('fastcode')) {
                this.loadConfig();
                this.updateClient();
            }
        });
    }

    private loadConfig(): void {
        const config = vscode.workspace.getConfiguration('fastcode');
        this.config = {
            endpoint: config.get('apiEndpoint', 'http://localhost:11434'),
            modelName: config.get('modelName', 'codellama:7b'),
            maxTokens: config.get('maxTokens', 2048),
            temperature: config.get('temperature', 0.1),
            timeout: 30000
        };
    }

    private updateClient(): void {
        this.client = axios.create({
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    async generateCompletion(request: AIRequest): Promise<AIResponse> {
        try {
            const response = await this.makeRequest(request);
            return this.parseResponse(response);
        } catch (error) {
            throw this.handleError(error);
        }
    }

    async generateStream(request: AIRequest, onChunk: (chunk: string) => void): Promise<void> {
        try {
            const response = await this.client.post(`${this.config.endpoint}/api/generate`, {
                model: this.config.modelName,
                prompt: this.buildPrompt(request),
                stream: true,
                options: {
                    temperature: request.temperature ?? this.config.temperature,
                    num_predict: request.maxTokens ?? this.config.maxTokens
                }
            }, {
                responseType: 'stream'
            });

            response.data.on('data', (chunk: Buffer) => {
                const lines = chunk.toString().split('\n').filter(line => line.trim());
                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        if (data.response) {
                            onChunk(data.response);
                        }
                    } catch (e) {
                        // Ignore parsing errors for incomplete chunks
                    }
                }
            });

            return new Promise((resolve, reject) => {
                response.data.on('end', resolve);
                response.data.on('error', reject);
            });
        } catch (error) {
            throw this.handleError(error);
        }
    }

    private async makeRequest(request: AIRequest): Promise<AxiosResponse> {
        const payload = this.buildPayload(request);
        
        // Try different API formats based on the endpoint
        const endpoints = [
            '/api/generate',  // Ollama format
            '/v1/completions', // OpenAI-compatible format
            '/api/v1/generate' // Alternative format
        ];

        let lastError: any;
        
        for (const endpoint of endpoints) {
            try {
                const response = await this.client.post(`${this.config.endpoint}${endpoint}`, payload);
                return response;
            } catch (error: any) {
                lastError = error;
                if (error.response?.status !== 404) {
                    break; // If it's not a 404, don't try other endpoints
                }
            }
        }
        
        throw lastError;
    }

    private buildPayload(request: AIRequest): any {
        const prompt = this.buildPrompt(request);
        
        // Try Ollama format first
        return {
            model: this.config.modelName,
            prompt: prompt,
            stream: false,
            options: {
                temperature: request.temperature ?? this.config.temperature,
                num_predict: request.maxTokens ?? this.config.maxTokens
            }
        };
    }

    private buildPrompt(request: AIRequest): string {
        let prompt = '';
        
        if (request.context) {
            prompt += `Context:\n${request.context}\n\n`;
        }
        
        prompt += `Request: ${request.prompt}`;
        
        return prompt;
    }

    private parseResponse(response: AxiosResponse): AIResponse {
        const data = response.data;
        
        // Handle Ollama format
        if (data.response) {
            return {
                content: data.response,
                usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens || 0,
                    completionTokens: data.usage.completion_tokens || 0,
                    totalTokens: data.usage.total_tokens || 0
                } : undefined
            };
        }
        
        // Handle OpenAI-compatible format
        if (data.choices && data.choices.length > 0) {
            return {
                content: data.choices[0].text || data.choices[0].message?.content || '',
                usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens || 0,
                    completionTokens: data.usage.completion_tokens || 0,
                    totalTokens: data.usage.total_tokens || 0
                } : undefined
            };
        }
        
        throw new Error('Unexpected response format from AI model');
    }

    private handleError(error: any): Error {
        if (error.code === 'ECONNREFUSED') {
            return new Error(`Cannot connect to AI model at ${this.config.endpoint}. Please ensure your local AI service is running.`);
        }
        
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error || error.response.statusText;
            return new Error(`AI model error (${status}): ${message}`);
        }
        
        if (error.code === 'ENOTFOUND') {
            return new Error(`Invalid AI model endpoint: ${this.config.endpoint}`);
        }
        
        return new Error(`AI model service error: ${error.message}`);
    }

    async testConnection(): Promise<boolean> {
        try {
            const response = await this.generateCompletion({
                prompt: 'Hello, are you working?',
                maxTokens: 10
            });
            return response.content.length > 0;
        } catch (error) {
            console.error('AI model connection test failed:', error);
            return false;
        }
    }

    getConfig(): ModelConfig {
        return { ...this.config };
    }
}
