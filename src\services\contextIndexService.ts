import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import Parser from 'tree-sitter';
import { Database } from 'sqlite3';

// Import tree-sitter language parsers
const TypeScript = require('tree-sitter-typescript').typescript;
const JavaScript = require('tree-sitter-javascript');
const Python = require('tree-sitter-python');
const Java = require('tree-sitter-java');
const Cpp = require('tree-sitter-cpp');
const Rust = require('tree-sitter-rust');
const Go = require('tree-sitter-go');

export interface CodeSymbol {
    name: string;
    type: 'function' | 'class' | 'variable' | 'interface' | 'method' | 'property';
    filePath: string;
    startLine: number;
    endLine: number;
    startColumn: number;
    endColumn: number;
    content: string;
    context: string;
    signature?: string;
    docstring?: string;
}

export interface FileIndex {
    filePath: string;
    lastModified: number;
    symbols: CodeSymbol[];
    imports: string[];
    exports: string[];
    dependencies: string[];
}

export interface SearchResult {
    symbol: CodeSymbol;
    score: number;
    relevantContext: string[];
}

export class ContextIndexService {
    private db!: Database;
    private parsers: Map<string, Parser> = new Map();
    private fileWatcher!: vscode.FileSystemWatcher;
    private indexedFiles: Map<string, FileIndex> = new Map();
    private isIndexing = false;

    constructor(private context: vscode.ExtensionContext) {
        this.initializeParsers();
        this.initializeDatabase();
        this.setupFileWatcher();
    }

    private initializeParsers(): void {
        const languages = {
            'typescript': TypeScript,
            'javascript': JavaScript,
            'python': Python,
            'java': Java,
            'cpp': Cpp,
            'c': Cpp,
            'rust': Rust,
            'go': Go
        };

        for (const [lang, Language] of Object.entries(languages)) {
            const parser = new Parser();
            parser.setLanguage(Language);
            this.parsers.set(lang, parser);
        }
    }

    private initializeDatabase(): void {
        const dbPath = path.join(this.context.globalStorageUri.fsPath, 'fastcode.db');
        
        // Ensure directory exists
        fs.mkdirSync(path.dirname(dbPath), { recursive: true });
        
        this.db = new Database(dbPath);
        
        // Create tables
        this.db.serialize(() => {
            this.db.run(`
                CREATE TABLE IF NOT EXISTS symbols (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    start_line INTEGER NOT NULL,
                    end_line INTEGER NOT NULL,
                    start_column INTEGER NOT NULL,
                    end_column INTEGER NOT NULL,
                    content TEXT NOT NULL,
                    context TEXT NOT NULL,
                    signature TEXT,
                    docstring TEXT,
                    last_modified INTEGER NOT NULL
                )
            `);
            
            this.db.run(`
                CREATE TABLE IF NOT EXISTS files (
                    file_path TEXT PRIMARY KEY,
                    last_modified INTEGER NOT NULL,
                    imports TEXT,
                    exports TEXT,
                    dependencies TEXT
                )
            `);
            
            this.db.run(`CREATE INDEX IF NOT EXISTS idx_symbols_name ON symbols(name)`);
            this.db.run(`CREATE INDEX IF NOT EXISTS idx_symbols_type ON symbols(type)`);
            this.db.run(`CREATE INDEX IF NOT EXISTS idx_symbols_file ON symbols(file_path)`);
        });
    }

    private setupFileWatcher(): void {
        const config = vscode.workspace.getConfiguration('fastcode');
        const indexedTypes = config.get<string[]>('indexedFileTypes', [
            'typescript', 'javascript', 'python', 'java', 'cpp', 'c', 'rust', 'go'
        ]);
        
        const pattern = `**/*.{${indexedTypes.join(',')}}`;
        this.fileWatcher = vscode.workspace.createFileSystemWatcher(pattern);
        
        this.fileWatcher.onDidCreate(uri => this.indexFile(uri.fsPath));
        this.fileWatcher.onDidChange(uri => this.indexFile(uri.fsPath));
        this.fileWatcher.onDidDelete(uri => this.removeFileFromIndex(uri.fsPath));
    }

    async initialize(): Promise<void> {
        const config = vscode.workspace.getConfiguration('fastcode');
        if (!config.get('enableContextIndex', true)) {
            return;
        }

        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'FastCode: Indexing workspace...',
            cancellable: false
        }, async (progress) => {
            await this.indexWorkspace(progress);
        });
    }

    private async indexWorkspace(progress?: vscode.Progress<{message?: string; increment?: number}>): Promise<void> {
        if (this.isIndexing) {
            return;
        }

        this.isIndexing = true;
        
        try {
            const config = vscode.workspace.getConfiguration('fastcode');
            const indexedTypes = config.get<string[]>('indexedFileTypes', [
                'typescript', 'javascript', 'python', 'java', 'cpp', 'c', 'rust', 'go'
            ]);
            
            const pattern = `**/*.{${indexedTypes.join(',')}}`;
            const files = await vscode.workspace.findFiles(pattern, '**/node_modules/**');
            
            let processed = 0;
            const total = files.length;
            
            for (const file of files) {
                await this.indexFile(file.fsPath);
                processed++;
                
                if (progress) {
                    progress.report({
                        message: `Indexed ${processed}/${total} files`,
                        increment: (100 / total)
                    });
                }
            }
        } finally {
            this.isIndexing = false;
        }
    }

    private async indexFile(filePath: string): Promise<void> {
        try {
            const stat = fs.statSync(filePath);
            const lastModified = stat.mtime.getTime();
            
            // Check if file needs reindexing
            const existingIndex = this.indexedFiles.get(filePath);
            if (existingIndex && existingIndex.lastModified >= lastModified) {
                return;
            }

            const content = fs.readFileSync(filePath, 'utf8');
            const language = this.getLanguageFromFile(filePath);
            const parser = this.parsers.get(language);
            
            if (!parser) {
                return;
            }

            const tree = parser.parse(content);
            const symbols = this.extractSymbols(tree, content, filePath);
            const imports = this.extractImports(tree, content, language);
            const exports = this.extractExports(tree, content, language);
            
            const fileIndex: FileIndex = {
                filePath,
                lastModified,
                symbols,
                imports,
                exports,
                dependencies: []
            };
            
            this.indexedFiles.set(filePath, fileIndex);
            await this.saveToDatabase(fileIndex);
            
        } catch (error) {
            console.error(`Error indexing file ${filePath}:`, error);
        }
    }

    private getLanguageFromFile(filePath: string): string {
        const ext = path.extname(filePath).toLowerCase();
        const mapping: { [key: string]: string } = {
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.c': 'c',
            '.h': 'cpp',
            '.hpp': 'cpp',
            '.rs': 'rust',
            '.go': 'go'
        };
        
        return mapping[ext] || 'javascript';
    }

    private extractSymbols(tree: Parser.Tree, content: string, filePath: string): CodeSymbol[] {
        const symbols: CodeSymbol[] = [];
        const lines = content.split('\n');
        
        const traverse = (node: Parser.SyntaxNode) => {
            const symbol = this.nodeToSymbol(node, lines, filePath);
            if (symbol) {
                symbols.push(symbol);
            }
            
            for (const child of node.children) {
                traverse(child);
            }
        };
        
        traverse(tree.rootNode);
        return symbols;
    }

    private nodeToSymbol(node: Parser.SyntaxNode, lines: string[], filePath: string): CodeSymbol | null {
        const typeMapping: { [key: string]: CodeSymbol['type'] } = {
            'function_declaration': 'function',
            'method_definition': 'method',
            'class_declaration': 'class',
            'interface_declaration': 'interface',
            'variable_declaration': 'variable',
            'property_definition': 'property'
        };
        
        const symbolType = typeMapping[node.type];
        if (!symbolType) {
            return null;
        }
        
        const nameNode = node.children.find(child => child.type === 'identifier');
        if (!nameNode) {
            return null;
        }
        
        const name = nameNode.text;
        const startPos = node.startPosition;
        const endPos = node.endPosition;
        
        const content = node.text;
        const context = this.getContextAroundNode(node, lines);
        
        return {
            name,
            type: symbolType,
            filePath,
            startLine: startPos.row + 1,
            endLine: endPos.row + 1,
            startColumn: startPos.column,
            endColumn: endPos.column,
            content,
            context,
            signature: this.extractSignature(node),
            docstring: this.extractDocstring(node, lines)
        };
    }

    private getContextAroundNode(node: Parser.SyntaxNode, lines: string[]): string {
        const startLine = Math.max(0, node.startPosition.row - 2);
        const endLine = Math.min(lines.length - 1, node.endPosition.row + 2);
        
        return lines.slice(startLine, endLine + 1).join('\n');
    }

    private extractSignature(node: Parser.SyntaxNode): string | undefined {
        // Extract function/method signature
        if (node.type === 'function_declaration' || node.type === 'method_definition') {
            const nameNode = node.children.find(child => child.type === 'identifier');
            const params = node.children.find(child => child.type === 'parameters');
            const returnType = node.children.find(child => child.type === 'type_annotation');

            let signature = nameNode?.text || '';
            if (params) {
                signature += params.text;
            }
            if (returnType) {
                signature += ': ' + returnType.text;
            }

            return signature;
        }

        return undefined;
    }

    private extractDocstring(node: Parser.SyntaxNode, lines: string[]): string | undefined {
        // Look for comments/docstrings before the node
        const startLine = node.startPosition.row;
        
        for (let i = startLine - 1; i >= 0; i--) {
            const line = lines[i].trim();
            if (line.startsWith('/**') || line.startsWith('"""') || line.startsWith('#')) {
                // Found potential docstring, extract it
                let docstring = '';
                let j = i;
                
                while (j < startLine && j < lines.length) {
                    docstring += lines[j] + '\n';
                    j++;
                }
                
                return docstring.trim();
            }
            
            if (line && !line.startsWith('//') && !line.startsWith('#')) {
                break; // Stop if we hit non-comment content
            }
        }
        
        return undefined;
    }

    private extractImports(tree: Parser.Tree, content: string, language: string): string[] {
        const imports: string[] = [];
        
        const traverse = (node: Parser.SyntaxNode) => {
            if (node.type === 'import_statement' || node.type === 'import_declaration') {
                imports.push(node.text);
            }
            
            for (const child of node.children) {
                traverse(child);
            }
        };
        
        traverse(tree.rootNode);
        return imports;
    }

    private extractExports(tree: Parser.Tree, content: string, language: string): string[] {
        const exports: string[] = [];
        
        const traverse = (node: Parser.SyntaxNode) => {
            if (node.type === 'export_statement' || node.type === 'export_declaration') {
                exports.push(node.text);
            }
            
            for (const child of node.children) {
                traverse(child);
            }
        };
        
        traverse(tree.rootNode);
        return exports;
    }

    private async saveToDatabase(fileIndex: FileIndex): Promise<void> {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                // Remove existing entries for this file
                this.db.run('DELETE FROM symbols WHERE file_path = ?', [fileIndex.filePath]);
                this.db.run('DELETE FROM files WHERE file_path = ?', [fileIndex.filePath]);
                
                // Insert file record
                this.db.run(
                    'INSERT INTO files (file_path, last_modified, imports, exports, dependencies) VALUES (?, ?, ?, ?, ?)',
                    [
                        fileIndex.filePath,
                        fileIndex.lastModified,
                        JSON.stringify(fileIndex.imports),
                        JSON.stringify(fileIndex.exports),
                        JSON.stringify(fileIndex.dependencies)
                    ]
                );
                
                // Insert symbols
                const stmt = this.db.prepare(`
                    INSERT INTO symbols (
                        name, type, file_path, start_line, end_line, start_column, end_column,
                        content, context, signature, docstring, last_modified
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `);
                
                for (const symbol of fileIndex.symbols) {
                    stmt.run([
                        symbol.name,
                        symbol.type,
                        symbol.filePath,
                        symbol.startLine,
                        symbol.endLine,
                        symbol.startColumn,
                        symbol.endColumn,
                        symbol.content,
                        symbol.context,
                        symbol.signature,
                        symbol.docstring,
                        fileIndex.lastModified
                    ]);
                }
                
                stmt.finalize((err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            });
        });
    }

    private async removeFileFromIndex(filePath: string): Promise<void> {
        this.indexedFiles.delete(filePath);
        
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                this.db.run('DELETE FROM symbols WHERE file_path = ?', [filePath]);
                this.db.run('DELETE FROM files WHERE file_path = ?', [filePath], (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            });
        });
    }

    async searchSymbols(query: string, type?: CodeSymbol['type']): Promise<SearchResult[]> {
        return new Promise((resolve, reject) => {
            let sql = 'SELECT * FROM symbols WHERE name LIKE ?';
            const params: any[] = [`%${query}%`];
            
            if (type) {
                sql += ' AND type = ?';
                params.push(type);
            }
            
            sql += ' ORDER BY name LIMIT 50';
            
            this.db.all(sql, params, (err, rows: any[]) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                const results: SearchResult[] = rows.map(row => ({
                    symbol: {
                        name: row.name,
                        type: row.type,
                        filePath: row.file_path,
                        startLine: row.start_line,
                        endLine: row.end_line,
                        startColumn: row.start_column,
                        endColumn: row.end_column,
                        content: row.content,
                        context: row.context,
                        signature: row.signature,
                        docstring: row.docstring
                    },
                    score: this.calculateRelevanceScore(query, row.name),
                    relevantContext: []
                }));
                
                resolve(results.sort((a, b) => b.score - a.score));
            });
        });
    }

    private calculateRelevanceScore(query: string, symbolName: string): number {
        const queryLower = query.toLowerCase();
        const nameLower = symbolName.toLowerCase();
        
        if (nameLower === queryLower) {
            return 100;
        }
        
        if (nameLower.startsWith(queryLower)) {
            return 80;
        }
        
        if (nameLower.includes(queryLower)) {
            return 60;
        }
        
        // Calculate fuzzy match score
        let score = 0;
        let queryIndex = 0;
        
        for (let i = 0; i < nameLower.length && queryIndex < queryLower.length; i++) {
            if (nameLower[i] === queryLower[queryIndex]) {
                score += 1;
                queryIndex++;
            }
        }
        
        return (score / queryLower.length) * 40;
    }

    async getContextForPosition(filePath: string, line: number, column: number): Promise<string> {
        const fileIndex = this.indexedFiles.get(filePath);
        if (!fileIndex) {
            return '';
        }
        
        // Find symbols at or near the position
        const relevantSymbols = fileIndex.symbols.filter(symbol => 
            symbol.startLine <= line && symbol.endLine >= line
        );
        
        if (relevantSymbols.length === 0) {
            return '';
        }
        
        // Return the context of the most specific symbol (smallest range)
        const mostSpecific = relevantSymbols.reduce((prev, current) => 
            (current.endLine - current.startLine) < (prev.endLine - prev.startLine) ? current : prev
        );
        
        return mostSpecific.context;
    }

    async getRelatedSymbols(symbolName: string, filePath: string): Promise<CodeSymbol[]> {
        // Find symbols that might be related (same name, imports, etc.)
        const results = await this.searchSymbols(symbolName);
        return results.map(r => r.symbol).filter(s => s.filePath !== filePath);
    }

    dispose(): void {
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }
        
        if (this.db) {
            this.db.close();
        }
    }
}
