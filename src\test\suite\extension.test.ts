import * as assert from 'assert';
import * as vscode from 'vscode';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('fastcode.fastcode'));
    });

    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('fastcode.fastcode');
        assert.ok(extension);
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        assert.ok(extension.isActive);
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        
        const expectedCommands = [
            'fastcode.inlineChat',
            'fastcode.composer',
            'fastcode.globalChat',
            'fastcode.autoDebug',
            'fastcode.generateTests',
            'fastcode.generateDocs',
            'fastcode.terminalCommand',
            'fastcode.semanticSearch'
        ];

        for (const command of expectedCommands) {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        }
    });
});

// Simplified tests to avoid complex type issues

suite('Integration Tests', () => {
    test('Should handle workspace without errors', async () => {
        // Test that the extension can handle an empty workspace
        const workspaceFolders = vscode.workspace.workspaceFolders;
        
        // This should not throw an error even if no workspace is open
        assert.ok(workspaceFolders === undefined || Array.isArray(workspaceFolders));
    });

    test('Should handle configuration changes', () => {
        const config = vscode.workspace.getConfiguration('fastcode');
        
        // Test that configuration exists and has expected structure
        assert.ok(config);
        
        // These should not throw errors
        const endpoint = config.get('apiEndpoint');
        const modelName = config.get('modelName');
        const maxTokens = config.get('maxTokens');
        const temperature = config.get('temperature');
        
        assert.ok(typeof endpoint === 'string' || endpoint === undefined);
        assert.ok(typeof modelName === 'string' || modelName === undefined);
        assert.ok(typeof maxTokens === 'number' || maxTokens === undefined);
        assert.ok(typeof temperature === 'number' || temperature === undefined);
    });
});
