import * as assert from 'assert';
import * as vscode from 'vscode';
import { AIModelService } from '../../services/aiModelService';
import { ContextIndexService } from '../../services/contextIndexService';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('fastcode.fastcode'));
    });

    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('fastcode.fastcode');
        assert.ok(extension);
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        assert.ok(extension.isActive);
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        
        const expectedCommands = [
            'fastcode.inlineChat',
            'fastcode.composer',
            'fastcode.globalChat',
            'fastcode.autoDebug',
            'fastcode.generateTests',
            'fastcode.generateDocs',
            'fastcode.terminalCommand',
            'fastcode.semanticSearch'
        ];

        for (const command of expectedCommands) {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        }
    });
});

suite('AIModelService Tests', () => {
    let aiService: AIModelService;

    setup(() => {
        aiService = new AIModelService();
    });

    test('Should initialize with default config', () => {
        const config = aiService.getConfig();
        assert.ok(config.endpoint);
        assert.ok(config.modelName);
        assert.ok(config.maxTokens > 0);
        assert.ok(config.temperature >= 0 && config.temperature <= 1);
    });

    test('Should handle connection test gracefully', async () => {
        // This test will likely fail in CI without a local model
        // but should not throw an exception
        try {
            const result = await aiService.testConnection();
            assert.ok(typeof result === 'boolean');
        } catch (error) {
            // Expected in test environment without local AI model
            assert.ok(error instanceof Error);
        }
    });
});

suite('ContextIndexService Tests', () => {
    let contextService: ContextIndexService;
    let mockContext: vscode.ExtensionContext;

    setup(() => {
        // Create a mock extension context
        mockContext = {
            globalStorageUri: vscode.Uri.file('/tmp/test-storage'),
            subscriptions: [],
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => []
            },
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                setKeysForSync: () => {},
                keys: () => []
            },
            extensionUri: vscode.Uri.file('/tmp/test-extension'),
            extensionPath: '/tmp/test-extension',
            asAbsolutePath: (relativePath: string) => `/tmp/test-extension/${relativePath}`,
            storageUri: vscode.Uri.file('/tmp/test-storage'),
            storagePath: '/tmp/test-storage',
            globalStoragePath: '/tmp/test-global-storage',
            logUri: vscode.Uri.file('/tmp/test-logs'),
            logPath: '/tmp/test-logs',
            extensionMode: vscode.ExtensionMode.Test,
            secrets: {
                get: () => Promise.resolve(undefined),
                store: () => Promise.resolve(),
                delete: () => Promise.resolve(),
                onDidChange: new vscode.EventEmitter<vscode.SecretStorageChangeEvent>().event
            },
            environmentVariableCollection: {
                persistent: true,
                replace: () => {},
                append: () => {},
                prepend: () => {},
                get: () => undefined,
                forEach: () => {},
                delete: () => {},
                clear: () => {},
                [Symbol.iterator]: function* () {}
            }
        } as vscode.ExtensionContext;

        contextService = new ContextIndexService(mockContext);
    });

    teardown(() => {
        if (contextService) {
            contextService.dispose();
        }
    });

    test('Should initialize without errors', () => {
        assert.ok(contextService);
    });

    test('Should handle search with empty results', async () => {
        const results = await contextService.searchSymbols('nonexistent_symbol');
        assert.ok(Array.isArray(results));
        assert.strictEqual(results.length, 0);
    });
});

suite('Integration Tests', () => {
    test('Should handle workspace without errors', async () => {
        // Test that the extension can handle an empty workspace
        const workspaceFolders = vscode.workspace.workspaceFolders;
        
        // This should not throw an error even if no workspace is open
        assert.ok(workspaceFolders === undefined || Array.isArray(workspaceFolders));
    });

    test('Should handle configuration changes', () => {
        const config = vscode.workspace.getConfiguration('fastcode');
        
        // Test that configuration exists and has expected structure
        assert.ok(config);
        
        // These should not throw errors
        const endpoint = config.get('apiEndpoint');
        const modelName = config.get('modelName');
        const maxTokens = config.get('maxTokens');
        const temperature = config.get('temperature');
        
        assert.ok(typeof endpoint === 'string' || endpoint === undefined);
        assert.ok(typeof modelName === 'string' || modelName === undefined);
        assert.ok(typeof maxTokens === 'number' || maxTokens === undefined);
        assert.ok(typeof temperature === 'number' || temperature === undefined);
    });
});
