实现一个vscode插件，可以加载本地大模型进行项目构建
名称：FastCode

核心 AI 能力总览
a. Inline Chat（行内对话）
任意代码行按下 ⌘/Ctrl + K → 弹出浮层，直接键入自然语言指令：
– “把这段同步代码改异步”
– “把 for 循环换成 lodash map”
– “添加边界错误处理，不要改函数签名”
回车后模型返回 diff，支持一键 Apply / Reject / Retry。
b. Composer（多文件生成器）
⌘/Ctrl + I 打开侧边 Composer，输入需求：
– “创建一个 Express + TypeScript + Prisma 的 REST API 项目骨架，包含用户注册登录 JWT 鉴权”
它会一次性生成 package.json、tsconfig.json、src/app.ts、src/routes/auth.ts、prisma/schema.prisma 等多个文件并自动保存。
c. Codebase-wide Chat（全局对话）
⌘/Ctrl + L 呼出全局 AI 面板，可跨文件问答：
– “整个项目里哪里用了未加索引的 Mongo 查询？”
– “解释一下 utils/validate.ts 与 middleware/auth.ts 之间的调用关系”
模型会先在本地做一次「文件索引 + 语义检索」，再给出答案与源文件跳转链接。
d. Auto-Debug（自动调试）
当终端报错时，光标悬停在错误行，按 ⌥/Alt + Enter → AI 自动解析堆栈，给出可能原因与修复 patch，并可直接 Apply。
e. Auto-Test（自动测试）
右键函数 → “Generate unit tests”，自动分析函数签名、依赖、边界条件，生成 Jest / PyTest / Go-test 等测试文件。
f. Auto-Doc（自动文档）
右键文件 → “Generate README” 或 “Generate docstring”，支持输出 Markdown、Sphinx、JSDoc 等格式。
g. Terminal Command Generator
按 ⌘/Ctrl + 
复制
 
打开终端，输入 # 开头自然语言：  
   – “# 找到 30 天内未修改的大文件”  
   终端会自动补全
find . -type f -mtime +30 -size +10M` 并等待回车执行。
h. Natural Language Search（自然语言搜索）
全局搜索框中输入 “所有包含用户权限校验的中间件”，AI 会返回匹配文件名及行号，而非纯文本匹配。
上下文系统
• Cursor 在本地维护一个「实时索引」：
– 使用 Tree-sitter 解析语法树；
– 使用向量数据库（Chroma/Milvus）做语义向量；
– 监听文件 change 事件即时增量更新。
• 当触发 AI 请求时，首先基于「光标位置 → 当前函数 → 当前文件 → 相关 import → 全局符号」四层优先级召回切片，再组合成 prompt，保证 token 不浪费。
模型接入与隐私
介入本地局域网部署的私有大模型api

快捷键全集
⌘/Ctrl + K        行内指令
⌘/Ctrl + I        Composer 多文件生成
⌘/Ctrl + L        全局对话
⌘/Ctrl + Enter    全局对话并附带当前文件
⌥/Alt + Enter     报错行自动修复
⌘/Ctrl + Shift + V  一键 Apply diff
⌘/Ctrl + Shift + R  重新生成上一次请求
与 VS Code 插件的兼容策略
• 99% 插件可直接安装，Cursor 启动时会检测冲突插件并提示：
– GitLens / Prettier / ESLint / Python / Rust-Analyzer 均可正常使用；
– 若 AI 功能与插件功能冲突，可在 settings.json 里 "cursor.overrideExtension": ["tabnine", "github-copilot"] 关闭相应插件。

典型工作流示例
场景：接手一个 6 万行的遗留 Python 项目，需要添加「用户积分」功能。
⌘/Ctrl + L → “解释 models.py 中 User 表的所有字段含义与关联关系”。
⌘/Ctrl + I → “在 User 模型增加积分字段，并创建积分变更日志表，同步生成 Alembic migration”。
自动生成后，在 service 目录选中 user.py → Inline Chat → “写一个积分消费接口，要求幂等、防止超卖”。
终端 # 生成测试数据脚本并运行。
运行发现报错 → Alt+Enter → AI 给出修复 patch → Apply → 测试通过。
⌘/Ctrl + Shift + L → “生成接口文档并保存到 docs/points.md”。
全程 10 分钟完成，无需离开编辑器。
